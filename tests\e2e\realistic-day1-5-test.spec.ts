/**
 * Realistic Day 1-5 Testing Suite
 * Practical automated tests for actual implementation
 */

import { test, expect } from '@playwright/test';

test.describe('Day 1-5 Realistic Testing Suite', () => {
  
  test.describe('Day 1: UI Revolution - Basic Functionality', () => {
    test('homepage loads successfully', async ({ page }) => {
      await page.goto('/');
      
      // Check page loads without errors
      await expect(page).toHaveTitle(/Nirmaan/i);
      
      // Check for basic content
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('body')).toBeVisible();
      
      // Check for no console errors
      const errors: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      await page.waitForTimeout(2000);
      expect(errors.length).toBe(0);
    });

    test('responsive design works', async ({ page }) => {
      await page.goto('/');
      
      // Test desktop view
      await page.setViewportSize({ width: 1200, height: 800 });
      await expect(page.locator('body')).toBeVisible();
      
      // Test mobile view
      await page.setViewportSize({ width: 375, height: 667 });
      await expect(page.locator('body')).toBeVisible();
      
      // Test tablet view
      await page.setViewportSize({ width: 768, height: 1024 });
      await expect(page.locator('body')).toBeVisible();
    });

    test('navigation elements are present', async ({ page }) => {
      await page.goto('/');
      
      // Look for common navigation elements
      const navElements = [
        'nav',
        'header',
        'a[href*="calculator"]',
        'button',
      ];
      
      for (const selector of navElements) {
        const element = page.locator(selector).first();
        await expect(element).toBeVisible();
      }
    });
  });

  test.describe('Day 2: Smart Form Wizard - Core Functionality', () => {
    test('calculator page loads', async ({ page }) => {
      await page.goto('/calculator');
      
      // Check page loads
      await expect(page.locator('body')).toBeVisible();
      
      // Look for form elements
      const formElements = page.locator('input, select, button');
      await expect(formElements.first()).toBeVisible();
    });

    test('form inputs are interactive', async ({ page }) => {
      await page.goto('/calculator');
      
      // Find text inputs and try to type
      const textInputs = page.locator('input[type="text"], input[type="number"]');
      const inputCount = await textInputs.count();
      
      if (inputCount > 0) {
        const firstInput = textInputs.first();
        await firstInput.fill('2000');
        await expect(firstInput).toHaveValue('2000');
      }
      
      // Find select elements and try to select
      const selects = page.locator('select');
      const selectCount = await selects.count();
      
      if (selectCount > 0) {
        const firstSelect = selects.first();
        const options = firstSelect.locator('option');
        const optionCount = await options.count();
        
        if (optionCount > 1) {
          await firstSelect.selectOption({ index: 1 });
        }
      }
    });

    test('form validation works', async ({ page }) => {
      await page.goto('/calculator');
      
      // Try to find and click a submit/next button
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      if (buttonCount > 0) {
        // Look for buttons with text like "Next", "Continue", "Calculate"
        const actionButtons = page.locator('button:has-text("Next"), button:has-text("Continue"), button:has-text("Calculate")');
        const actionButtonCount = await actionButtons.count();
        
        if (actionButtonCount > 0) {
          const firstActionButton = actionButtons.first();
          await firstActionButton.click();
          
          // Check if page still exists (validation might prevent navigation)
          await expect(page.locator('body')).toBeVisible();
        }
      }
    });
  });

  test.describe('Day 3: Results Enhancement - Display Functionality', () => {
    test('results page can be accessed', async ({ page }) => {
      // Try to access results page directly
      await page.goto('/results');
      
      // Check if page loads (might redirect to calculator if no data)
      await expect(page.locator('body')).toBeVisible();
    });

    test('charts and visualizations load', async ({ page }) => {
      await page.goto('/results');
      
      // Look for chart containers or canvas elements
      const chartElements = [
        'canvas',
        'svg',
        '[class*="chart"]',
        '[class*="recharts"]',
      ];
      
      for (const selector of chartElements) {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          await expect(elements.first()).toBeVisible();
          break; // Found at least one chart element
        }
      }
    });
  });

  test.describe('Day 4: User System - Authentication Flow', () => {
    test('auth pages load', async ({ page }) => {
      const authPages = ['/auth/login', '/auth/signup', '/login', '/signup'];
      
      for (const authPage of authPages) {
        try {
          await page.goto(authPage);
          await expect(page.locator('body')).toBeVisible();
          
          // Look for auth form elements
          const authElements = page.locator('input[type="email"], input[type="password"], form');
          const count = await authElements.count();
          
          if (count > 0) {
            await expect(authElements.first()).toBeVisible();
            break; // Found working auth page
          }
        } catch (error) {
          // Continue to next auth page if this one doesn't exist
          continue;
        }
      }
    });

    test('dashboard page loads', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Check if page loads (might redirect to auth if not logged in)
      await expect(page.locator('body')).toBeVisible();
      
      // Look for dashboard elements
      const dashboardElements = [
        '[class*="dashboard"]',
        '[class*="stats"]',
        '[class*="card"]',
        'h1, h2, h3',
      ];
      
      for (const selector of dashboardElements) {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          await expect(elements.first()).toBeVisible();
          break;
        }
      }
    });
  });

  test.describe('Day 5: Admin Panel - Administrative Interface', () => {
    test('admin page loads', async ({ page }) => {
      await page.goto('/admin');
      
      // Check if page loads (might redirect to auth if not admin)
      await expect(page.locator('body')).toBeVisible();
      
      // Look for admin elements
      const adminElements = [
        '[class*="admin"]',
        '[class*="sidebar"]',
        'nav',
        '[class*="header"]',
      ];
      
      for (const selector of adminElements) {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          await expect(elements.first()).toBeVisible();
          break;
        }
      }
    });

    test('admin navigation works', async ({ page }) => {
      await page.goto('/admin');
      
      // Look for navigation links
      const navLinks = page.locator('a[href*="/admin"]');
      const linkCount = await navLinks.count();
      
      if (linkCount > 0) {
        // Try clicking the first admin navigation link
        const firstLink = navLinks.first();
        await firstLink.click();
        
        // Verify we're still on an admin page
        await expect(page).toHaveURL(/\/admin/);
      }
    });
  });

  test.describe('Cross-Day Integration Tests', () => {
    test('complete user flow simulation', async ({ page }) => {
      // Start from homepage
      await page.goto('/');
      await expect(page.locator('body')).toBeVisible();
      
      // Try to navigate to calculator
      const calculatorLinks = page.locator('a[href*="calculator"], button:has-text("Calculate"), button:has-text("Start")');
      const linkCount = await calculatorLinks.count();
      
      if (linkCount > 0) {
        await calculatorLinks.first().click();
        await expect(page.locator('body')).toBeVisible();
        
        // Fill some basic form data if inputs are available
        const inputs = page.locator('input[type="text"], input[type="number"]');
        const inputCount = await inputs.count();
        
        if (inputCount > 0) {
          await inputs.first().fill('2000');
        }
        
        // Try to proceed if there's a next/continue button
        const proceedButtons = page.locator('button:has-text("Next"), button:has-text("Continue")');
        const proceedCount = await proceedButtons.count();
        
        if (proceedCount > 0) {
          await proceedButtons.first().click();
          await expect(page.locator('body')).toBeVisible();
        }
      }
    });

    test('error handling works', async ({ page }) => {
      // Test 404 page
      await page.goto('/nonexistent-page');
      await expect(page.locator('body')).toBeVisible();
      
      // Test that we can navigate back to a working page
      await page.goto('/');
      await expect(page.locator('body')).toBeVisible();
    });

    test('performance is acceptable', async ({ page }) => {
      const startTime = Date.now();
      await page.goto('/');
      const loadTime = Date.now() - startTime;
      
      // Page should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
      
      // Check for basic performance metrics
      const performanceEntries = await page.evaluate(() => {
        return JSON.stringify(performance.getEntriesByType('navigation'));
      });
      
      expect(performanceEntries).toBeTruthy();
    });
  });

  test.describe('Accessibility and Quality Tests', () => {
    test('basic accessibility requirements', async ({ page }) => {
      await page.goto('/');
      
      // Check for proper document structure
      await expect(page.locator('html[lang]')).toBeVisible();
      await expect(page.locator('title')).toBeVisible();
      
      // Check for heading structure
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0);
      
      // Check for alt text on images
      const images = page.locator('img');
      const imageCount = await images.count();
      
      for (let i = 0; i < Math.min(imageCount, 5); i++) {
        const img = images.nth(i);
        const alt = await img.getAttribute('alt');
        expect(alt).toBeTruthy();
      }
    });

    test('no console errors on main pages', async ({ page }) => {
      const errors: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      const pagesToTest = ['/', '/calculator', '/dashboard', '/admin'];
      
      for (const pagePath of pagesToTest) {
        try {
          await page.goto(pagePath);
          await page.waitForTimeout(1000);
        } catch (error) {
          // Page might not exist or require auth, continue
          continue;
        }
      }
      
      // Filter out known acceptable errors
      const criticalErrors = errors.filter(error => 
        !error.includes('favicon') && 
        !error.includes('404') &&
        !error.includes('net::ERR_')
      );
      
      expect(criticalErrors.length).toBe(0);
    });
  });
});
