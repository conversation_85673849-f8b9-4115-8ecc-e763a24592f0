/**
 * Comprehensive Validation Script
 * Validates all Day 1 and Day 2 components and functionality
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function checkDirectoryExists(dirPath) {
  return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
}

function validateComponent(componentPath, componentName) {
  if (checkFileExists(componentPath)) {
    const content = fs.readFileSync(componentPath, 'utf8');
    const hasExport = content.includes('export') || content.includes('module.exports');
    const hasReact = content.includes('React') || content.includes('import');
    
    if (hasExport && hasReact) {
      log(`✅ ${componentName}: VALID`, 'green');
      return true;
    } else {
      log(`⚠️  ${componentName}: Missing exports or React imports`, 'yellow');
      return false;
    }
  } else {
    log(`❌ ${componentName}: FILE NOT FOUND`, 'red');
    return false;
  }
}

function validateDirectory(dirPath, dirName) {
  if (checkDirectoryExists(dirPath)) {
    const files = fs.readdirSync(dirPath);
    log(`✅ ${dirName}: ${files.length} files`, 'green');
    return true;
  } else {
    log(`❌ ${dirName}: DIRECTORY NOT FOUND`, 'red');
    return false;
  }
}

// Main validation function
function runComprehensiveValidation() {
  log('\n🚀 COMPREHENSIVE VALIDATION STARTING...', 'bold');
  log('=' * 60, 'blue');
  
  let totalTests = 0;
  let passedTests = 0;
  
  // Day 1: UI Revolution Components
  log('\n📋 DAY 1: UI REVOLUTION VALIDATION', 'blue');
  log('-' * 40, 'blue');
  
  const day1Components = [
    ['src/components/ui/button.tsx', 'Enhanced Button'],
    ['src/components/ui/input.tsx', 'Enhanced Input'],
    ['src/components/ui/card.tsx', 'Enhanced Card'],
    ['src/components/ui/enhanced-card.tsx', 'Enhanced Card with Variants'],
    ['src/components/ui/enhanced-button.tsx', 'Enhanced Button with Loading'],
    ['src/components/ui/mobile-input.tsx', 'Mobile-Optimized Input'],
    ['src/components/ui/contextual-help.tsx', 'Contextual Help System'],
    ['src/components/ui/loading-states.tsx', 'Loading States'],
    ['src/components/ui/skeleton.tsx', 'Skeleton Components'],
    ['src/components/ui/error-boundary.tsx', 'Error Boundary'],
  ];
  
  day1Components.forEach(([path, name]) => {
    totalTests++;
    if (validateComponent(path, name)) passedTests++;
  });
  
  // Day 2: Smart Form Wizard Components
  log('\n📋 DAY 2: SMART FORM WIZARD VALIDATION', 'blue');
  log('-' * 40, 'blue');
  
  const day2Components = [
    ['src/components/calculator/FormWizard.tsx', 'Main Form Wizard'],
    ['src/components/calculator/WizardProgress.tsx', 'Wizard Progress'],
    ['src/components/calculator/WizardNavigation.tsx', 'Wizard Navigation'],
    ['src/components/calculator/steps/BasicInfoStep.tsx', 'Basic Info Step'],
    ['src/components/calculator/steps/RoomConfigStep.tsx', 'Room Config Step'],
    ['src/components/calculator/steps/QualitySelectionStep.tsx', 'Quality Selection Step'],
    ['src/components/calculator/steps/AdvancedFeaturesStep.tsx', 'Advanced Features Step'],
    ['src/components/calculator/types/wizard.ts', 'Wizard Types'],
    ['src/components/calculator/CalculatorContainer.tsx', 'Calculator Container'],
  ];
  
  day2Components.forEach(([path, name]) => {
    totalTests++;
    if (validateComponent(path, name)) passedTests++;
  });
  
  // Core Infrastructure
  log('\n📋 CORE INFRASTRUCTURE VALIDATION', 'blue');
  log('-' * 40, 'blue');
  
  const coreComponents = [
    ['src/core/calculator/engine.ts', 'Calculation Engine'],
    ['src/core/calculator/types.ts', 'Calculator Types'],
    ['src/core/calculator/validation.ts', 'Input Validation'],
    ['src/core/calculator/constants.ts', 'Calculator Constants'],
    ['src/app/api/calculate/route.ts', 'API Route Handler'],
    ['src/app/calculator/page.tsx', 'Calculator Page'],
    ['src/app/layout.tsx', 'Root Layout'],
  ];
  
  coreComponents.forEach(([path, name]) => {
    totalTests++;
    if (validateComponent(path, name)) passedTests++;
  });
  
  // Hooks and Utilities
  log('\n📋 HOOKS AND UTILITIES VALIDATION', 'blue');
  log('-' * 40, 'blue');
  
  const hooks = [
    ['src/hooks/useSmartDefaults.ts', 'Smart Defaults Hook'],
    ['src/hooks/useRecommendations.ts', 'Recommendations Hook'],
    ['src/hooks/useCalculator.ts', 'Calculator Hook'],
    ['src/hooks/useMobileUX.ts', 'Mobile UX Hook'],
    ['src/lib/utils.ts', 'Utility Functions'],
    ['src/lib/mobile/index.ts', 'Mobile Utilities'],
  ];
  
  hooks.forEach(([path, name]) => {
    totalTests++;
    if (validateComponent(path, name)) passedTests++;
  });
  
  // Configuration Files
  log('\n📋 CONFIGURATION VALIDATION', 'blue');
  log('-' * 40, 'blue');
  
  const configFiles = [
    ['package.json', 'Package Configuration'],
    ['tsconfig.json', 'TypeScript Configuration'],
    ['tailwind.config.ts', 'Tailwind Configuration'],
    ['next.config.js', 'Next.js Configuration'],
    ['components.json', 'shadcn/ui Configuration'],
  ];
  
  configFiles.forEach(([path, name]) => {
    totalTests++;
    if (checkFileExists(path)) {
      log(`✅ ${name}: EXISTS`, 'green');
      passedTests++;
    } else {
      log(`❌ ${name}: NOT FOUND`, 'red');
    }
  });
  
  // Directory Structure
  log('\n📋 DIRECTORY STRUCTURE VALIDATION', 'blue');
  log('-' * 40, 'blue');
  
  const directories = [
    ['src/components/ui', 'UI Components'],
    ['src/components/calculator', 'Calculator Components'],
    ['src/components/calculator/steps', 'Wizard Steps'],
    ['src/core/calculator', 'Calculator Engine'],
    ['src/hooks', 'React Hooks'],
    ['src/lib', 'Utility Libraries'],
    ['src/app', 'Next.js App Directory'],
    ['tests/e2e', 'E2E Tests'],
  ];
  
  directories.forEach(([path, name]) => {
    totalTests++;
    if (validateDirectory(path, name)) passedTests++;
  });
  
  // Final Results
  log('\n📊 VALIDATION RESULTS', 'bold');
  log('=' * 60, 'blue');
  log(`Total Tests: ${totalTests}`, 'blue');
  log(`Passed: ${passedTests}`, 'green');
  log(`Failed: ${totalTests - passedTests}`, 'red');
  log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`, 'yellow');
  
  if (passedTests === totalTests) {
    log('\n🎉 ALL VALIDATIONS PASSED! DAY 1 & DAY 2 ARE 100% COMPLETE!', 'green');
    log('✅ Ready for production deployment!', 'green');
  } else {
    log('\n⚠️  Some validations failed. Review the issues above.', 'yellow');
  }
  
  // Generate report
  const report = {
    timestamp: new Date().toISOString(),
    totalTests,
    passedTests,
    failedTests: totalTests - passedTests,
    successRate: Math.round((passedTests / totalTests) * 100),
    status: passedTests === totalTests ? 'COMPLETE' : 'INCOMPLETE'
  };
  
  fs.writeFileSync('validation-report.json', JSON.stringify(report, null, 2));
  log('\n📄 Report saved to: validation-report.json', 'blue');
  
  return passedTests === totalTests;
}

// Run the validation
if (require.main === module) {
  runComprehensiveValidation();
}

module.exports = { runComprehensiveValidation };
