/**
 * Admin Sidebar Component
 * Day 5 Task 1: Admin Panel Foundation
 * Navigation sidebar for admin panel
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  LayoutDashboard,
  Package,
  Users,
  FileText,
  BarChart3,
  Settings,
  DollarSign,
  MessageSquare,
  Shield,
  Database,
  Zap,
  ChevronRight,
  Home,
  Activity,
  HelpCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdminSidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

const navigation = [
  {
    title: "Overview",
    items: [
      { name: "Dashboard", href: "/admin", icon: LayoutDashboard, badge: null },
      { name: "Analytics", href: "/admin/analytics", icon: BarChart3, badge: null },
      { name: "Activity", href: "/admin/activity", icon: Activity, badge: "12" },
    ]
  },
  {
    title: "Content Management",
    items: [
      { name: "Materials", href: "/admin/materials", icon: Package, badge: null },
      { name: "Categories", href: "/admin/categories", icon: Database, badge: null },
      { name: "Pricing Rules", href: "/admin/pricing", icon: DollarSign, badge: "3" },
    ]
  },
  {
    title: "User Management",
    items: [
      { name: "Users", href: "/admin/users", icon: Users, badge: null },
      { name: "Roles & Permissions", href: "/admin/roles", icon: Shield, badge: null },
      { name: "Activity Logs", href: "/admin/logs", icon: FileText, badge: null },
    ]
  },
  {
    title: "Support",
    items: [
      { name: "Support Tickets", href: "/admin/support", icon: MessageSquare, badge: "5" },
      { name: "Feedback", href: "/admin/feedback", icon: HelpCircle, badge: "2" },
    ]
  },
  {
    title: "System",
    items: [
      { name: "Settings", href: "/admin/settings", icon: Settings, badge: null },
      { name: "Integrations", href: "/admin/integrations", icon: Zap, badge: null },
    ]
  }
];

export function AdminSidebar({ collapsed, onToggle }: AdminSidebarProps) {
  const pathname = usePathname();

  return (
    <motion.div
      className={cn(
        "fixed inset-y-0 left-0 z-30 bg-white border-r border-gray-200 pt-16 transition-all duration-300",
        collapsed ? "w-16" : "w-64"
      )}
      initial={false}
      animate={{ width: collapsed ? 64 : 256 }}
    >
      <div className="flex flex-col h-full">
        {/* Navigation */}
        <div className="flex-1 overflow-y-auto py-4">
          {navigation.map((section) => (
            <div key={section.title} className="mb-6">
              {!collapsed && (
                <h3 className="px-6 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  {section.title}
                </h3>
              )}
              <nav className="space-y-1">
                {section.items.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "group flex items-center transition-colors relative",
                        collapsed ? "px-4 py-3 mx-2 rounded-lg" : "px-6 py-2.5",
                        isActive
                          ? "bg-primary-50 text-primary-700 border-r-2 border-primary-700"
                          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      )}
                      title={collapsed ? item.name : undefined}
                    >
                      <item.icon
                        className={cn(
                          "flex-shrink-0 transition-colors",
                          collapsed ? "h-6 w-6" : "mr-3 h-5 w-5",
                          isActive
                            ? "text-primary-700"
                            : "text-gray-400 group-hover:text-gray-500"
                        )}
                      />
                      
                      {!collapsed && (
                        <>
                          <span className="flex-1 text-sm font-medium">
                            {item.name}
                          </span>
                          
                          {/* Badge */}
                          {item.badge && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              {item.badge}
                            </span>
                          )}
                          
                          {/* Active Indicator */}
                          {isActive && (
                            <ChevronRight className="ml-auto h-4 w-4 text-primary-700" />
                          )}
                        </>
                      )}

                      {/* Collapsed Badge */}
                      {collapsed && item.badge && (
                        <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-500 text-white min-w-[18px] h-[18px]">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  );
                })}
              </nav>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        {!collapsed && (
          <div className="p-4 border-t border-gray-200">
            <div className="space-y-2">
              <Link
                href="/dashboard"
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <Home className="mr-3 h-4 w-4" />
                Back to App
              </Link>
            </div>
          </div>
        )}

        {/* Admin Info */}
        <div className={cn(
          "p-4 border-t border-gray-200",
          collapsed && "px-2"
        )}>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={cn(
                "rounded-full bg-primary-100 flex items-center justify-center",
                collapsed ? "h-8 w-8" : "h-10 w-10"
              )}>
                <Shield className={cn(
                  "text-primary-600",
                  collapsed ? "h-4 w-4" : "h-5 w-5"
                )} />
              </div>
            </div>
            {!collapsed && (
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  Admin Panel
                </p>
                <p className="text-xs text-gray-500">
                  v1.0.0
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
