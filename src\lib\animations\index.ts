/**
 * Animation Utilities and Variants
 * Provides consistent animations across the application
 */

import { type Variants } from 'framer-motion';

// Check if user prefers reduced motion
export const prefersReducedMotion = typeof window !== 'undefined' 
  ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
  : false;

// Base animation variants
export const fadeIn: Variants = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.3 } },
  exit: { opacity: 0, transition: { duration: 0.2 } }
};

export const slideUp: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  exit: { opacity: 0, y: -20, transition: { duration: 0.2 } }
};

export const slideDown: Variants = {
  initial: { opacity: 0, y: -20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  exit: { opacity: 0, y: 20, transition: { duration: 0.2 } }
};

export const slideLeft: Variants = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0, transition: { duration: 0.3 } },
  exit: { opacity: 0, x: -20, transition: { duration: 0.2 } }
};

export const slideRight: Variants = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0, transition: { duration: 0.3 } },
  exit: { opacity: 0, x: 20, transition: { duration: 0.2 } }
};

export const scaleIn: Variants = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { opacity: 1, scale: 1, transition: { duration: 0.3 } },
  exit: { opacity: 0, scale: 0.9, transition: { duration: 0.2 } }
};

// Stagger animations
export const staggerContainer: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

export const staggerItem: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.3 }
  }
};

// Shimmer loading animation
export const shimmerLoading = {
  animate: {
    backgroundPosition: ['200px 0', '-200px 0'],
    transition: {
      duration: 1.5,
      ease: 'linear',
      repeat: Infinity
    }
  }
};

// Page transition variants
export const pageTransition: Variants = {
  initial: { opacity: 0, x: 20 },
  animate: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.4, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0, 
    x: -20,
    transition: { duration: 0.3, ease: 'easeIn' }
  }
};

// Modal/Dialog animations
export const modalOverlay: Variants = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.2 } },
  exit: { opacity: 0, transition: { duration: 0.2 } }
};

export const modalContent: Variants = {
  initial: { opacity: 0, scale: 0.95, y: 20 },
  animate: { 
    opacity: 1, 
    scale: 1, 
    y: 0,
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0, 
    scale: 0.95, 
    y: 20,
    transition: { duration: 0.2, ease: 'easeIn' }
  }
};

// Button animations
export const buttonHover = {
  scale: 1.02,
  transition: { duration: 0.2 }
};

export const buttonTap = {
  scale: 0.98,
  transition: { duration: 0.1 }
};

// Card animations
export const cardHover = {
  y: -4,
  boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
  transition: { duration: 0.2 }
};

// Loading spinner animation
export const spinnerRotate = {
  rotate: 360,
  transition: {
    duration: 1,
    ease: 'linear',
    repeat: Infinity
  }
};

// Progress bar animation
export const progressFill = (width: number) => ({
  width: `${width}%`,
  transition: { duration: 0.5, ease: 'easeOut' }
});

// Notification animations
export const notificationSlide: Variants = {
  initial: { opacity: 0, x: 100, scale: 0.9 },
  animate: { 
    opacity: 1, 
    x: 0, 
    scale: 1,
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0, 
    x: 100, 
    scale: 0.9,
    transition: { duration: 0.2, ease: 'easeIn' }
  }
};

// Form field animations
export const fieldFocus = {
  scale: 1.02,
  transition: { duration: 0.2 }
};

export const fieldError = {
  x: [-2, 2, -2, 2, 0],
  transition: { duration: 0.4 }
};

// List item animations
export const listItemHover = {
  backgroundColor: 'rgba(0, 0, 0, 0.02)',
  transition: { duration: 0.2 }
};

// Utility functions
export const getReducedMotionVariant = (variant: Variants): Variants => {
  if (prefersReducedMotion) {
    return {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 }
    };
  }
  return variant;
};

export const createStaggerVariants = (staggerDelay: number = 0.1): Variants => ({
  initial: {},
  animate: {
    transition: {
      staggerChildren: staggerDelay,
      delayChildren: staggerDelay
    }
  }
});

// Animation presets for common use cases
export const animationPresets = {
  // Page transitions
  pageSlideLeft: slideLeft,
  pageSlideRight: slideRight,
  pageFade: fadeIn,
  
  // Component animations
  cardEnter: scaleIn,
  listEnter: slideUp,
  modalEnter: modalContent,
  
  // Loading states
  skeleton: shimmerLoading,
  spinner: spinnerRotate,
  
  // Interactive elements
  buttonPress: { scale: 0.95 },
  cardPress: { scale: 0.98 },
  
  // Notifications
  toast: notificationSlide,
  alert: slideDown
};

export default {
  fadeIn,
  slideUp,
  slideDown,
  slideLeft,
  slideRight,
  scaleIn,
  staggerContainer,
  staggerItem,
  shimmerLoading,
  pageTransition,
  modalOverlay,
  modalContent,
  buttonHover,
  buttonTap,
  cardHover,
  spinnerRotate,
  progressFill,
  notificationSlide,
  fieldFocus,
  fieldError,
  listItemHover,
  getReducedMotionVariant,
  createStaggerVariants,
  animationPresets,
  prefersReducedMotion
};
