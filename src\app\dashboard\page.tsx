/**
 * User Dashboard Page
 * Day 4 Task 2: User Dashboard Creation (3 hours)
 * Complete dashboard with project management and insights
 */

'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Calculator,
  Download,
  Share2,
  Edit3,
  Trash2,
  Copy,
  TrendingUp,
  Home,
  MapPin,
  Calendar,
  DollarSign,
  BarChart3,
  Users,
  Settings,
  Bell,
  Search,
  Filter,
  Grid,
  List,
  Eye,
  Star,
  Clock,
} from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { MobileInput } from '@/components/ui/mobile-input';
import { cn } from '@/lib/utils';

// Mock data types
interface Project {
  id: string;
  name: string;
  location: string;
  plotSize: number;
  builtUpArea: number;
  totalCost: number;
  costPerSqft: number;
  qualityTier: 'smart' | 'premium' | 'luxury';
  status: 'draft' | 'finalized' | 'in-progress' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  thumbnail?: string;
  progress?: number;
}

interface DashboardStats {
  totalProjects: number;
  totalInvestment: number;
  averageCostPerSqft: number;
  completedProjects: number;
}

interface Activity {
  id: string;
  type: 'created' | 'updated' | 'shared' | 'downloaded';
  projectName: string;
  timestamp: Date;
  description: string;
}

// Mock data
const mockProjects: Project[] = [
  {
    id: '1',
    name: 'Dream Villa Project',
    location: 'Bangalore',
    plotSize: 2400,
    builtUpArea: 1800,
    totalCost: 4500000,
    costPerSqft: 2500,
    qualityTier: 'premium',
    status: 'in-progress',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
    progress: 65,
  },
  {
    id: '2',
    name: 'Compact Home Design',
    location: 'Chennai',
    plotSize: 1200,
    builtUpArea: 900,
    totalCost: 1800000,
    costPerSqft: 2000,
    qualityTier: 'smart',
    status: 'finalized',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
    progress: 100,
  },
  {
    id: '3',
    name: 'Luxury Residence',
    location: 'Mumbai',
    plotSize: 3000,
    builtUpArea: 2200,
    totalCost: 8800000,
    costPerSqft: 4000,
    qualityTier: 'luxury',
    status: 'draft',
    createdAt: new Date('2024-01-22'),
    updatedAt: new Date('2024-01-22'),
    progress: 25,
  },
];

const mockActivities: Activity[] = [
  {
    id: '1',
    type: 'created',
    projectName: 'Luxury Residence',
    timestamp: new Date('2024-01-22T10:30:00'),
    description: 'Created new project calculation',
  },
  {
    id: '2',
    type: 'updated',
    projectName: 'Dream Villa Project',
    timestamp: new Date('2024-01-20T15:45:00'),
    description: 'Updated material specifications',
  },
  {
    id: '3',
    type: 'downloaded',
    projectName: 'Compact Home Design',
    timestamp: new Date('2024-01-18T09:15:00'),
    description: 'Downloaded PDF report',
  },
];

export default function DashboardPage() {
  const [projects, setProjects] = useState<Project[]>(mockProjects);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'cost' | 'name'>('date');

  // Calculate dashboard stats
  const dashboardStats: DashboardStats = useMemo(() => {
    return {
      totalProjects: projects.length,
      totalInvestment: projects.reduce((sum, p) => sum + p.totalCost, 0),
      averageCostPerSqft: Math.round(
        projects.reduce((sum, p) => sum + p.costPerSqft, 0) / projects.length
      ),
      completedProjects: projects.filter(p => p.status === 'completed').length,
    };
  }, [projects]);

  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    let filtered = projects;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(p =>
        p.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        p.location.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(p => p.status === filterStatus);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return b.updatedAt.getTime() - a.updatedAt.getTime();
        case 'cost':
          return b.totalCost - a.totalCost;
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

    return filtered;
  }, [projects, searchQuery, filterStatus, sortBy]);

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) return `₹${(amount / 10000000).toFixed(1)}Cr`;
    if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'finalized': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getQualityColor = (quality: Project['qualityTier']) => {
    switch (quality) {
      case 'smart': return 'text-green-600';
      case 'premium': return 'text-blue-600';
      case 'luxury': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  const handleProjectAction = (action: string, projectId: string) => {
    switch (action) {
      case 'edit':
        // Navigate to calculator with project data
        console.log('Edit project:', projectId);
        break;
      case 'duplicate':
        // Duplicate project
        console.log('Duplicate project:', projectId);
        break;
      case 'delete':
        // Delete project
        setProjects(prev => prev.filter(p => p.id !== projectId));
        break;
      case 'download':
        // Download PDF
        console.log('Download PDF for project:', projectId);
        break;
      case 'share':
        // Share project
        console.log('Share project:', projectId);
        break;
    }
  };

  const handleBulkAction = (action: string) => {
    switch (action) {
      case 'delete':
        setProjects(prev => prev.filter(p => !selectedProjects.includes(p.id)));
        setSelectedProjects([]);
        break;
      case 'download':
        console.log('Download PDFs for projects:', selectedProjects);
        break;
      case 'compare':
        console.log('Compare projects:', selectedProjects);
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
              <span className="text-sm text-gray-500">Welcome back, John!</span>
            </div>
            <div className="flex items-center gap-3">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Bell className="h-5 w-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Settings className="h-5 w-5" />
              </button>
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">J</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <EnhancedCard variant="default" size="md">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Projects</p>
                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.totalProjects}</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <Home className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>
            </EnhancedCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <EnhancedCard variant="default" size="md">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Investment</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(dashboardStats.totalInvestment)}</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-lg">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </div>
            </EnhancedCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <EnhancedCard variant="default" size="md">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Avg Cost/sq ft</p>
                    <p className="text-2xl font-bold text-gray-900">₹{dashboardStats.averageCostPerSqft.toLocaleString('en-IN')}</p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </div>
            </EnhancedCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <EnhancedCard variant="default" size="md">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.completedProjects}</p>
                  </div>
                  <div className="p-3 bg-yellow-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </div>
            </EnhancedCard>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Projects Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">Your Projects</h2>
              <EnhancedButton
                variant="default"
                leftIcon={<Plus className="h-4 w-4" />}
                onClick={() => console.log('New calculation')}
              >
                New Calculation
              </EnhancedButton>
            </div>

            {/* Filters and Search */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <MobileInput
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={setSearchQuery}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="finalized">Finalized</option>
                  <option value="in-progress">In Progress</option>
                  <option value="completed">Completed</option>
                </select>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
                >
                  <option value="date">Sort by Date</option>
                  <option value="cost">Sort by Cost</option>
                  <option value="name">Sort by Name</option>
                </select>
                <div className="flex border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={cn(
                      'p-2 text-sm',
                      viewMode === 'grid' ? 'bg-primary-500 text-white' : 'text-gray-600 hover:text-gray-900'
                    )}
                  >
                    <Grid className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={cn(
                      'p-2 text-sm',
                      viewMode === 'list' ? 'bg-primary-500 text-white' : 'text-gray-600 hover:text-gray-900'
                    )}
                  >
                    <List className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Bulk Actions */}
            {selectedProjects.length > 0 && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-between">
                <span className="text-sm text-blue-800">
                  {selectedProjects.length} project(s) selected
                </span>
                <div className="flex gap-2">
                  <EnhancedButton
                    variant="outline"
                    size="sm"
                    onClick={() => handleBulkAction('compare')}
                    disabled={selectedProjects.length < 2}
                  >
                    Compare
                  </EnhancedButton>
                  <EnhancedButton
                    variant="outline"
                    size="sm"
                    onClick={() => handleBulkAction('download')}
                  >
                    Download All
                  </EnhancedButton>
                  <EnhancedButton
                    variant="outline"
                    size="sm"
                    onClick={() => handleBulkAction('delete')}
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    Delete
                  </EnhancedButton>
                </div>
              </div>
            )}

            {/* Projects Grid/List */}
            <div className={cn(
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 gap-6'
                : 'space-y-4'
            )}>
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <EnhancedCard 
                    variant="default" 
                    size="md"
                    className={cn(
                      'cursor-pointer transition-all duration-200 hover:shadow-lg',
                      selectedProjects.includes(project.id) && 'ring-2 ring-primary-500'
                    )}
                  >
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-3">
                          <input
                            type="checkbox"
                            checked={selectedProjects.includes(project.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedProjects(prev => [...prev, project.id]);
                              } else {
                                setSelectedProjects(prev => prev.filter(id => id !== project.id));
                              }
                            }}
                            className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                          <div>
                            <h3 className="font-semibold text-gray-900">{project.name}</h3>
                            <div className="flex items-center gap-1 text-sm text-gray-600 mt-1">
                              <MapPin className="h-3 w-3" />
                              {project.location}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={cn('px-2 py-1 rounded-full text-xs font-medium', getStatusColor(project.status))}>
                            {project.status.replace('-', ' ')}
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                        <div>
                          <span className="text-gray-600">Plot Size:</span>
                          <span className="ml-1 font-medium">{project.plotSize} sq ft</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Built-up:</span>
                          <span className="ml-1 font-medium">{project.builtUpArea} sq ft</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Total Cost:</span>
                          <span className="ml-1 font-medium">{formatCurrency(project.totalCost)}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Quality:</span>
                          <span className={cn('ml-1 font-medium capitalize', getQualityColor(project.qualityTier))}>
                            {project.qualityTier}
                          </span>
                        </div>
                      </div>

                      {project.progress !== undefined && (
                        <div className="mb-4">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">{project.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${project.progress}%` }}
                            />
                          </div>
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          Updated {project.updatedAt.toLocaleDateString()}
                        </div>
                        <div className="flex items-center gap-1">
                          <button
                            onClick={() => handleProjectAction('edit', project.id)}
                            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                            title="Edit"
                          >
                            <Edit3 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleProjectAction('duplicate', project.id)}
                            className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                            title="Duplicate"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleProjectAction('download', project.id)}
                            className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
                            title="Download PDF"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleProjectAction('share', project.id)}
                            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                            title="Share"
                          >
                            <Share2 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleProjectAction('delete', project.id)}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </EnhancedCard>
                </motion.div>
              ))}
            </div>

            {filteredProjects.length === 0 && (
              <div className="text-center py-12">
                <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                <p className="text-gray-600 mb-4">
                  {searchQuery || filterStatus !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'Start by creating your first construction project'
                  }
                </p>
                <EnhancedButton
                  variant="default"
                  leftIcon={<Plus className="h-4 w-4" />}
                  onClick={() => console.log('New calculation')}
                >
                  Create New Project
                </EnhancedButton>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <EnhancedCard variant="default" size="md">
              <div className="p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <EnhancedButton
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    leftIcon={<Calculator className="h-4 w-4" />}
                  >
                    New Calculation
                  </EnhancedButton>
                  <EnhancedButton
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    leftIcon={<BarChart3 className="h-4 w-4" />}
                  >
                    Compare Projects
                  </EnhancedButton>
                  <EnhancedButton
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    leftIcon={<Download className="h-4 w-4" />}
                  >
                    Download All Reports
                  </EnhancedButton>
                  <EnhancedButton
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    leftIcon={<Share2 className="h-4 w-4" />}
                  >
                    Share Portfolio
                  </EnhancedButton>
                </div>
              </div>
            </EnhancedCard>

            {/* Recent Activity */}
            <EnhancedCard variant="default" size="md">
              <div className="p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Recent Activity</h3>
                <div className="space-y-3">
                  {mockActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3">
                      <div className="p-1 bg-blue-100 rounded-full">
                        <div className="w-2 h-2 bg-blue-600 rounded-full" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900">{activity.description}</p>
                        <p className="text-xs text-gray-600">{activity.projectName}</p>
                        <p className="text-xs text-gray-500">
                          {activity.timestamp.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </EnhancedCard>

            {/* Insights */}
            <EnhancedCard variant="default" size="md">
              <div className="p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Insights</h3>
                <div className="space-y-4">
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm text-green-800">
                      <strong>Cost Optimization:</strong> You could save ₹3.2L by choosing smart quality tier for your next project.
                    </p>
                  </div>
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Trending:</strong> Premium quality is most popular in your area this month.
                    </p>
                  </div>
                  <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                    <p className="text-sm text-purple-800">
                      <strong>Market Update:</strong> Steel prices have decreased by 5% this quarter.
                    </p>
                  </div>
                </div>
              </div>
            </EnhancedCard>
          </div>
        </div>
      </div>
    </div>
  );
}
