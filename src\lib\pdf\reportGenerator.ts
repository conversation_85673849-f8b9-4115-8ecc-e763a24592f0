/**
 * Professional PDF Report Generator
 * Day 3 Task 3: PDF Report Generation with jsPDF
 * Creates comprehensive construction cost reports
 */

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface ReportData {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: { amount: number; percentage: number };
    finishing: { amount: number; percentage: number };
    mep: { amount: number; percentage: number };
    external: { amount: number; percentage: number };
    other: { amount: number; percentage: number };
  };
  builtUpArea: number;
  plotSize: number;
  floors: number;
  location: string;
  quality: string;
  buildingType: string;
  projectName?: string;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
}

interface ReportOptions {
  includeCharts?: boolean;
  includeTimeline?: boolean;
  includeMaterials?: boolean;
  includeTerms?: boolean;
  watermark?: boolean;
  companyLogo?: string;
}

export class ProfessionalReportGenerator {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin: number;
  private currentY: number;
  private primaryColor: string;
  private secondaryColor: string;

  constructor() {
    this.doc = new jsPDF('p', 'mm', 'a4');
    this.pageWidth = this.doc.internal.pageSize.getWidth();
    this.pageHeight = this.doc.internal.pageSize.getHeight();
    this.margin = 20;
    this.currentY = this.margin;
    this.primaryColor = '#3B82F6'; // Blue
    this.secondaryColor = '#6B7280'; // Gray
  }

  async generateReport(data: ReportData, options: ReportOptions = {}): Promise<Blob> {
    try {
      // Add cover page
      await this.addCoverPage(data);
      
      // Add executive summary
      this.addNewPage();
      await this.addExecutiveSummary(data);
      
      // Add detailed breakdown
      this.addNewPage();
      await this.addDetailedBreakdown(data);
      
      // Add material specifications
      if (options.includeMaterials) {
        this.addNewPage();
        await this.addMaterialSpecifications(data);
      }
      
      // Add timeline
      if (options.includeTimeline) {
        this.addNewPage();
        await this.addConstructionTimeline(data);
      }
      
      // Add terms and conditions
      if (options.includeTerms) {
        this.addNewPage();
        await this.addTermsAndConditions();
      }
      
      // Add footer to all pages
      this.addFooterToAllPages();
      
      return this.doc.output('blob');
    } catch (error) {
      console.error('PDF generation failed:', error);
      throw new Error('Failed to generate PDF report');
    }
  }

  private addNewPage(): void {
    this.doc.addPage();
    this.currentY = this.margin;
  }

  private addFooterToAllPages(): void {
    const totalPages = this.doc.getNumberOfPages();
    
    for (let i = 1; i <= totalPages; i++) {
      this.doc.setPage(i);
      
      // Footer line
      this.doc.setDrawColor(200, 200, 200);
      this.doc.line(this.margin, this.pageHeight - 15, this.pageWidth - this.margin, this.pageHeight - 15);
      
      // Footer text
      this.doc.setFontSize(8);
      this.doc.setTextColor(100, 100, 100);
      this.doc.text(
        'Generated by Nirmaan AI Construction Calculator',
        this.margin,
        this.pageHeight - 10
      );
      
      // Page number
      this.doc.text(
        `Page ${i} of ${totalPages}`,
        this.pageWidth - this.margin - 20,
        this.pageHeight - 10
      );
      
      // Date
      this.doc.text(
        new Date().toLocaleDateString('en-IN'),
        this.pageWidth / 2 - 15,
        this.pageHeight - 10
      );
    }
  }

  private async addCoverPage(data: ReportData): Promise<void> {
    // Header with gradient background (simulated with rectangles)
    this.doc.setFillColor(59, 130, 246); // Primary blue
    this.doc.rect(0, 0, this.pageWidth, 60, 'F');
    
    // Company logo placeholder
    this.doc.setFillColor(255, 255, 255);
    this.doc.circle(this.pageWidth / 2, 30, 15, 'F');
    
    // Company name
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(24);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('NIRMAAN AI', this.pageWidth / 2, 75, { align: 'center' });
    
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text('Construction Cost Estimation Report', this.pageWidth / 2, 85, { align: 'center' });
    
    // Project title
    this.currentY = 110;
    this.doc.setTextColor(0, 0, 0);
    this.doc.setFontSize(20);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(
      data.projectName || `${data.buildingType} Construction Project`,
      this.pageWidth / 2,
      this.currentY,
      { align: 'center' }
    );
    
    // Project details box
    this.currentY += 30;
    this.doc.setFillColor(248, 250, 252);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 80, 'F');
    this.doc.setDrawColor(226, 232, 240);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 80, 'S');
    
    // Project details content
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    this.doc.setTextColor(0, 0, 0);
    
    const details = [
      `Location: ${data.location}`,
      `Plot Size: ${data.plotSize} sq ft`,
      `Built-up Area: ${data.builtUpArea} sq ft`,
      `Floors: ${data.floors === 0 ? 'Ground only' : `Ground + ${data.floors}`}`,
      `Quality Tier: ${data.quality.charAt(0).toUpperCase() + data.quality.slice(1)}`,
      `Total Cost: ${this.formatCurrency(data.totalCost)}`,
      `Cost per sq ft: ₹${data.costPerSqft.toLocaleString('en-IN')}`,
    ];
    
    details.forEach((detail, index) => {
      this.doc.text(detail, this.margin + 10, this.currentY + 15 + (index * 8));
    });
    
    // Client information (if provided)
    if (data.clientName) {
      this.currentY += 100;
      this.doc.setFontSize(14);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text('Client Information', this.margin, this.currentY);
      
      this.doc.setFontSize(12);
      this.doc.setFont('helvetica', 'normal');
      this.doc.text(`Name: ${data.clientName}`, this.margin, this.currentY + 15);
      if (data.clientEmail) {
        this.doc.text(`Email: ${data.clientEmail}`, this.margin, this.currentY + 25);
      }
      if (data.clientPhone) {
        this.doc.text(`Phone: ${data.clientPhone}`, this.margin, this.currentY + 35);
      }
    }
    
    // Report metadata
    this.currentY = this.pageHeight - 80;
    this.doc.setFillColor(59, 130, 246);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 40, 'F');
    
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.text(`Report Generated: ${new Date().toLocaleDateString('en-IN')}`, this.margin + 10, this.currentY + 15);
    this.doc.text(`Reference: NRM-${Date.now().toString().slice(-6)}`, this.margin + 10, this.currentY + 25);
    this.doc.text('Valid for: 30 days from generation date', this.margin + 10, this.currentY + 35);
  }

  private async addExecutiveSummary(data: ReportData): Promise<void> {
    // Page title
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.setTextColor(59, 130, 246);
    this.doc.text('Executive Summary', this.margin, this.currentY);
    
    this.currentY += 20;
    
    // Summary content
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    this.doc.setTextColor(0, 0, 0);
    
    const summaryText = `This report provides a comprehensive cost estimation for the construction of a ${data.buildingType.toLowerCase()} project in ${data.location}. The total estimated cost is ${this.formatCurrency(data.totalCost)} for a built-up area of ${data.builtUpArea} sq ft, resulting in a cost of ₹${data.costPerSqft.toLocaleString('en-IN')} per square foot.`;
    
    const splitText = this.doc.splitTextToSize(summaryText, this.pageWidth - 2 * this.margin);
    this.doc.text(splitText, this.margin, this.currentY);
    this.currentY += splitText.length * 6 + 10;
    
    // Key highlights
    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Key Highlights', this.margin, this.currentY);
    this.currentY += 15;
    
    const highlights = [
      `Total Project Cost: ${this.formatCurrency(data.totalCost)}`,
      `Cost per Square Foot: ₹${data.costPerSqft.toLocaleString('en-IN')}`,
      `Quality Tier: ${data.quality.charAt(0).toUpperCase() + data.quality.slice(1)} grade materials and finishes`,
      `Estimated Timeline: 8-12 months (depending on approvals and weather)`,
      `Accuracy: ±12% (based on current market rates)`,
    ];
    
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    
    highlights.forEach((highlight, index) => {
      this.doc.text(`• ${highlight}`, this.margin + 5, this.currentY + (index * 8));
    });
    
    this.currentY += highlights.length * 8 + 20;
    
    // Cost breakdown table
    this.addCostBreakdownTable(data);
  }

  private addCostBreakdownTable(data: ReportData): void {
    const tableData = [
      ['Category', 'Amount', 'Percentage'],
      ['Structure & Foundation', this.formatCurrency(data.breakdown.structure.amount), `${data.breakdown.structure.percentage}%`],
      ['Finishing Works', this.formatCurrency(data.breakdown.finishing.amount), `${data.breakdown.finishing.percentage}%`],
      ['MEP Works', this.formatCurrency(data.breakdown.mep.amount), `${data.breakdown.mep.percentage}%`],
      ['External Works', this.formatCurrency(data.breakdown.external.amount), `${data.breakdown.external.percentage}%`],
      ['Other Costs', this.formatCurrency(data.breakdown.other.amount), `${data.breakdown.other.percentage}%`],
      ['TOTAL', this.formatCurrency(data.totalCost), '100%'],
    ];
    
    const colWidths = [60, 50, 30];
    const rowHeight = 8;
    const tableWidth = colWidths.reduce((sum, width) => sum + width, 0);
    const startX = (this.pageWidth - tableWidth) / 2;
    
    // Table header
    this.doc.setFillColor(59, 130, 246);
    this.doc.rect(startX, this.currentY, tableWidth, rowHeight, 'F');
    
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFont('helvetica', 'bold');
    this.doc.setFontSize(10);
    
    let currentX = startX;
    tableData[0].forEach((header, index) => {
      this.doc.text(header, currentX + 2, this.currentY + 5);
      currentX += colWidths[index];
    });
    
    this.currentY += rowHeight;
    
    // Table rows
    this.doc.setTextColor(0, 0, 0);
    this.doc.setFont('helvetica', 'normal');
    
    for (let i = 1; i < tableData.length; i++) {
      const isTotal = i === tableData.length - 1;
      
      if (isTotal) {
        this.doc.setFillColor(248, 250, 252);
        this.doc.rect(startX, this.currentY, tableWidth, rowHeight, 'F');
        this.doc.setFont('helvetica', 'bold');
      }
      
      currentX = startX;
      tableData[i].forEach((cell, index) => {
        this.doc.text(cell, currentX + 2, this.currentY + 5);
        currentX += colWidths[index];
      });
      
      // Draw row border
      this.doc.setDrawColor(226, 232, 240);
      this.doc.line(startX, this.currentY + rowHeight, startX + tableWidth, this.currentY + rowHeight);
      
      this.currentY += rowHeight;
      
      if (isTotal) {
        this.doc.setFont('helvetica', 'normal');
      }
    }
    
    // Draw table border
    this.doc.setDrawColor(226, 232, 240);
    this.doc.rect(startX, this.currentY - (tableData.length * rowHeight), tableWidth, tableData.length * rowHeight, 'S');
  }

  private async addDetailedBreakdown(data: ReportData): Promise<void> {
    // Implementation for detailed breakdown page
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.setTextColor(59, 130, 246);
    this.doc.text('Detailed Cost Breakdown', this.margin, this.currentY);
    
    this.currentY += 20;
    
    // Add detailed breakdown content here
    // This would include category-wise detailed costs, material specifications, etc.
  }

  private async addMaterialSpecifications(data: ReportData): Promise<void> {
    // Implementation for material specifications page
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.setTextColor(59, 130, 246);
    this.doc.text('Material Specifications', this.margin, this.currentY);
    
    // Add material specifications content here
  }

  private async addConstructionTimeline(data: ReportData): Promise<void> {
    // Implementation for construction timeline page
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.setTextColor(59, 130, 246);
    this.doc.text('Construction Timeline', this.margin, this.currentY);
    
    // Add timeline content here
  }

  private async addTermsAndConditions(): Promise<void> {
    // Implementation for terms and conditions page
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.setTextColor(59, 130, 246);
    this.doc.text('Terms & Conditions', this.margin, this.currentY);
    
    // Add terms and conditions content here
  }

  private formatCurrency(amount: number): string {
    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)}Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    } else {
      return `₹${amount.toLocaleString('en-IN')}`;
    }
  }
}

// Export function for easy use
export async function generateConstructionReport(
  data: ReportData,
  options: ReportOptions = {}
): Promise<Blob> {
  const generator = new ProfessionalReportGenerator();
  return await generator.generateReport(data, options);
}

export default ProfessionalReportGenerator;
