'use client';

import { useState } from 'react';
import { CalculatorErrorBoundary } from '@/components/ui/error-boundary';
import FormWizard from './FormWizard';

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    other: number;
  };
  builtUpArea: number;
  quality: string;
  location: string;
}

export function CalculatorContainer() {
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleResult = (calculationResult: CalculationResult) => {
    setResult(calculationResult);
    setError(null);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setResult(null);
  };

  return (
    <CalculatorErrorBoundary>
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Enhanced MVP Form Wizard */}
        <FormWizard
          onResult={handleResult}
          onError={handleError}
          className="w-full"
        />

        {/* Results Display */}
        {result && (
          <div className="bg-white rounded-lg shadow-lg p-6 space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Cost Estimation Results</h2>
            
            {/* Summary */}
            <div className="bg-blue-50 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Total Estimated Cost
              </h3>
              <p className="text-3xl font-bold text-blue-600">
                ₹{result.totalCost.toLocaleString('en-IN')}
              </p>
              <p className="text-sm text-gray-600 mt-2">
                ₹{result.costPerSqft.toLocaleString('en-IN')} per sq ft
              </p>
            </div>

            {/* Breakdown */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span className="font-medium">Structure & Foundation</span>
                <span className="font-semibold">
                  ₹{result.breakdown.structure.toLocaleString('en-IN')}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span className="font-medium">Finishing & Interiors</span>
                <span className="font-semibold">
                  ₹{result.breakdown.finishing.toLocaleString('en-IN')}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span className="font-medium">MEP Systems</span>
                <span className="font-semibold">
                  ₹{result.breakdown.mep.toLocaleString('en-IN')}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span className="font-medium">Professional Fees & Others</span>
                <span className="font-semibold">
                  ₹{result.breakdown.other.toLocaleString('en-IN')}
                </span>
              </div>
            </div>

            {/* Project Details */}
            <div className="border-t pt-4">
              <h4 className="font-semibold mb-2">Project Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                <p>Built-up Area: {result.builtUpArea} sq ft</p>
                <p>Quality: {result.quality.charAt(0).toUpperCase() + result.quality.slice(1)}</p>
                <p>Location: {result.location.charAt(0).toUpperCase() + result.location.slice(1)}</p>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 font-medium">Error:</p>
            <p className="text-red-700">{error}</p>
          </div>
        )}
      </div>
    </CalculatorErrorBoundary>
  );
}
