/**
 * Interactive Charts Component
 * Day 3 Task 2: Interactive Visualizations with Recharts
 * Professional charts with animations and interactivity
 */

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart,
} from 'recharts';
import { 
  TrendingUp, 
  <PERSON><PERSON><PERSON>3, 
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Download,
  Maximize2
} from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { cn } from '@/lib/utils';

interface ChartData {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: { amount: number; percentage: number };
    finishing: { amount: number; percentage: number };
    mep: { amount: number; percentage: number };
    external: { amount: number; percentage: number };
    other: { amount: number; percentage: number };
  };
  builtUpArea: number;
  location: string;
  quality: string;
}

interface InteractiveChartsProps {
  data: ChartData;
  className?: string;
}

// Chart color palette
const COLORS = {
  structure: '#3B82F6',    // Blue
  finishing: '#10B981',    // Green
  mep: '#F59E0B',         // Yellow
  external: '#8B5CF6',    // Purple
  other: '#6B7280',       // Gray
  primary: '#6366F1',     // Indigo
  secondary: '#EC4899',   // Pink
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value.toLocaleString('en-IN')}
            {entry.payload.percentage && ` (${entry.payload.percentage}%)`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Custom label for pie chart
const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      className="text-sm font-medium"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

export function InteractiveCharts({ data, className }: InteractiveChartsProps) {
  const [activeChart, setActiveChart] = useState<string>('breakdown');
  const [hoveredSection, setHoveredSection] = useState<string | null>(null);

  // Prepare data for different chart types
  const pieChartData = useMemo(() => [
    { name: 'Structure & Foundation', value: data.breakdown.structure.amount, percentage: data.breakdown.structure.percentage, color: COLORS.structure },
    { name: 'Finishing Works', value: data.breakdown.finishing.amount, percentage: data.breakdown.finishing.percentage, color: COLORS.finishing },
    { name: 'MEP Works', value: data.breakdown.mep.amount, percentage: data.breakdown.mep.percentage, color: COLORS.mep },
    { name: 'External Works', value: data.breakdown.external.amount, percentage: data.breakdown.external.percentage, color: COLORS.external },
    { name: 'Other Costs', value: data.breakdown.other.amount, percentage: data.breakdown.other.percentage, color: COLORS.other },
  ], [data.breakdown]);

  const barChartData = useMemo(() => [
    { category: 'Structure', amount: data.breakdown.structure.amount, color: COLORS.structure },
    { category: 'Finishing', amount: data.breakdown.finishing.amount, color: COLORS.finishing },
    { category: 'MEP', amount: data.breakdown.mep.amount, color: COLORS.mep },
    { category: 'External', amount: data.breakdown.external.amount, color: COLORS.external },
    { category: 'Other', amount: data.breakdown.other.amount, color: COLORS.other },
  ], [data.breakdown]);

  // Sample comparison data (would come from API in real app)
  const comparisonData = useMemo(() => [
    { tier: 'Smart', cost: data.quality === 'smart' ? data.costPerSqft : data.costPerSqft * 0.8, current: data.quality === 'smart' },
    { tier: 'Premium', cost: data.quality === 'premium' ? data.costPerSqft : data.costPerSqft * 1.2, current: data.quality === 'premium' },
    { tier: 'Luxury', cost: data.quality === 'luxury' ? data.costPerSqft : data.costPerSqft * 1.5, current: data.quality === 'luxury' },
  ], [data.costPerSqft, data.quality]);

  // Sample timeline data
  const timelineData = useMemo(() => [
    { month: 'Month 1', foundation: 15, structure: 0, finishing: 0, total: 15 },
    { month: 'Month 2', foundation: 25, structure: 5, finishing: 0, total: 30 },
    { month: 'Month 3', foundation: 35, structure: 15, finishing: 0, total: 50 },
    { month: 'Month 4', foundation: 35, structure: 25, finishing: 0, total: 60 },
    { month: 'Month 5', foundation: 35, structure: 35, finishing: 5, total: 75 },
    { month: 'Month 6', foundation: 35, structure: 35, finishing: 15, total: 85 },
    { month: 'Month 7', foundation: 35, structure: 35, finishing: 25, total: 95 },
    { month: 'Month 8', foundation: 35, structure: 35, finishing: 30, total: 100 },
  ], []);

  const chartTypes = [
    { id: 'breakdown', name: 'Cost Breakdown', icon: PieChartIcon },
    { id: 'comparison', name: 'Quality Comparison', icon: BarChart3 },
    { id: 'timeline', name: 'Construction Timeline', icon: TrendingUp },
  ];

  const formatCurrency = (value: number) => {
    if (value >= 10000000) return `₹${(value / 10000000).toFixed(1)}Cr`;
    if (value >= 100000) return `₹${(value / 100000).toFixed(1)}L`;
    return `₹${value.toLocaleString('en-IN')}`;
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Chart Type Selector */}
      <div className="flex flex-wrap gap-2 justify-center">
        {chartTypes.map((type) => (
          <EnhancedButton
            key={type.id}
            variant={activeChart === type.id ? 'default' : 'outline'}
            size="sm"
            leftIcon={<type.icon className="h-4 w-4" />}
            onClick={() => setActiveChart(type.id)}
            className="transition-all duration-200"
          >
            {type.name}
          </EnhancedButton>
        ))}
      </div>

      {/* Chart Container */}
      <EnhancedCard variant="default" size="xl" className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-secondary-900">
            {chartTypes.find(t => t.id === activeChart)?.name}
          </h3>
          <div className="flex gap-2">
            <EnhancedButton
              variant="ghost"
              size="sm"
              leftIcon={<Download className="h-4 w-4" />}
              onClick={() => {
                // Export chart as image
                console.log('Export chart');
              }}
            >
              Export
            </EnhancedButton>
            <EnhancedButton
              variant="ghost"
              size="sm"
              leftIcon={<Maximize2 className="h-4 w-4" />}
              onClick={() => {
                // Fullscreen chart
                console.log('Fullscreen chart');
              }}
            >
              Fullscreen
            </EnhancedButton>
          </div>
        </div>

        <div className="h-96">
          {/* Cost Breakdown Pie Chart */}
          {activeChart === 'breakdown' && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                  animationBegin={0}
                  animationDuration={1000}
                  onMouseEnter={(_, index) => setHoveredSection(pieChartData[index].name)}
                  onMouseLeave={() => setHoveredSection(null)}
                >
                  {pieChartData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.color}
                      stroke={hoveredSection === entry.name ? '#fff' : 'none'}
                      strokeWidth={hoveredSection === entry.name ? 3 : 0}
                    />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  formatter={(value, entry: any) => (
                    <span style={{ color: entry.color }}>{value}</span>
                  )}
                />
              </PieChart>
            </ResponsiveContainer>
          )}

          {/* Quality Comparison Bar Chart */}
          {activeChart === 'comparison' && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={comparisonData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="tier" />
                <YAxis tickFormatter={formatCurrency} />
                <Tooltip 
                  content={<CustomTooltip />}
                  formatter={(value: number) => [formatCurrency(value), 'Cost per sq ft']}
                />
                <Bar 
                  dataKey="cost" 
                  fill={COLORS.primary}
                  animationDuration={1000}
                  radius={[4, 4, 0, 0]}
                >
                  {comparisonData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.current ? COLORS.primary : COLORS.secondary}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}

          {/* Construction Timeline Area Chart */}
          {activeChart === 'timeline' && (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={timelineData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                <Tooltip 
                  content={<CustomTooltip />}
                  formatter={(value: number) => [`${value}%`, 'Progress']}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="foundation"
                  stackId="1"
                  stroke={COLORS.structure}
                  fill={COLORS.structure}
                  animationDuration={1500}
                />
                <Area
                  type="monotone"
                  dataKey="structure"
                  stackId="1"
                  stroke={COLORS.finishing}
                  fill={COLORS.finishing}
                  animationDuration={1500}
                />
                <Area
                  type="monotone"
                  dataKey="finishing"
                  stackId="1"
                  stroke={COLORS.mep}
                  fill={COLORS.mep}
                  animationDuration={1500}
                />
              </AreaChart>
            </ResponsiveContainer>
          )}
        </div>

        {/* Chart Insights */}
        <div className="mt-6 p-4 bg-secondary-50 rounded-lg">
          <h4 className="font-medium text-secondary-900 mb-2">Key Insights</h4>
          {activeChart === 'breakdown' && (
            <ul className="text-sm text-secondary-700 space-y-1">
              <li>• Structure & Foundation represents the largest cost component at {data.breakdown.structure.percentage}%</li>
              <li>• Finishing works account for {data.breakdown.finishing.percentage}% of total cost</li>
              <li>• MEP systems are {data.breakdown.mep.percentage}% of budget - ensure quality electrical work</li>
            </ul>
          )}
          {activeChart === 'comparison' && (
            <ul className="text-sm text-secondary-700 space-y-1">
              <li>• Your {data.quality} choice offers excellent value for money</li>
              <li>• Premium tier costs ~20% more but includes branded materials</li>
              <li>• Luxury tier adds premium finishes and high-end fixtures</li>
            </ul>
          )}
          {activeChart === 'timeline' && (
            <ul className="text-sm text-secondary-700 space-y-1">
              <li>• Foundation work should be completed in first 3 months</li>
              <li>• Structure phase is critical - ensure quality supervision</li>
              <li>• Finishing phase allows for customization and upgrades</li>
            </ul>
          )}
        </div>
      </EnhancedCard>
    </div>
  );
}

export default InteractiveCharts;
