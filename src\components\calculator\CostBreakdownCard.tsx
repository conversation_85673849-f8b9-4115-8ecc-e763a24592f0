'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronRight, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CostBreakdown } from '@/core/calculator/types';
import { formatCurrency } from '@/lib/validation/calculator';
import { 
  progressBar, 
  staggeredCards, 
  cardStaggerItem, 
  percentageProgress,
  createAdvancedStagger,
  createHoverAnimation,
  enhancedCountUp
} from '@/lib/animations';
import { isMobileViewport, hapticFeedback } from '@/lib/mobile';

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    other: number;
  };
  builtUpArea: number;
  quality: string;
  location: string;
}

interface CostBreakdownCardProps {
  result?: CalculationResult;
  breakdown?: CostBreakdown;
  title?: string;
  amount?: number;
  percentage?: number;
  subcategories?: Record<string, number>;
  totalCost?: number;
  description?: string;
  className?: string;
}

interface CategoryData {
  name: string;
  description: string;
  icon: string;
}

const categoryData: Record<string, CategoryData> = {
  structure: {
    name: 'Structure & Foundation',
    description: 'Foundation, columns, beams, slabs, and structural elements',
    icon: '🏗️',
  },
  finishing: {
    name: 'Finishing & Interiors',
    description: 'Flooring, painting, doors, windows, and interior work',
    icon: '🎨',
  },
  mep: {
    name: 'MEP Systems',
    description: 'Mechanical, Electrical, and Plumbing installations',
    icon: '⚡',
  },
  external: {
    name: 'External Works',
    description: 'Compound wall, landscaping, gate, and external features',
    icon: '🌿',
  },
  other: {
    name: 'Professional Fees & Others',
    description: 'Architect fees, approvals, contingency, and miscellaneous',
    icon: '📋',
  },
};

// Single category component for mobile bottom sheets
function SingleCategoryCard({
  title,
  amount,
  percentage,
  subcategories,
  totalCost,
}: {
  title: string;
  amount: number;
  percentage: number;
  subcategories?: Record<string, number>;
  totalCost: number;
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    hapticFeedback.light();
  };

  const categoryKey = title.toLowerCase().replace(/\s+/g, '');
  const categoryInfo = categoryData[categoryKey] || {
    name: title,
    description: 'Cost breakdown for this category',
    icon: '📊',
  };

  return (
    <motion.div
      {...createHoverAnimation(1.02, 0, '0 8px 30px rgba(0, 0, 0, 0.12)')}
    >
      <Card className="overflow-hidden">
        <CardHeader
          className={cn(
            'cursor-pointer transition-colors',
            mobileClasses.touchTarget,
            'hover:bg-gray-50'
          )}
          onClick={toggleExpanded}
        >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="text-xl">{categoryInfo.icon}</div>
            <div className="flex-1">
              <CardTitle className="text-base font-semibold">
                {categoryInfo.name}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {categoryInfo.description}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="text-right">
              <motion.div 
                className="text-lg font-bold text-gray-900"
                variants={enhancedCountUp}
                initial="initial"
                animate="animate"
              >
                {formatCurrency(amount)}
              </motion.div>
              <div className="text-xs text-gray-500">
                {percentage.toFixed(1)}% of total
              </div>
            </div>

            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="h-5 w-5 text-gray-400" />
            </motion.div>
          </div>
        </div>

        {/* Enhanced Progress bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full"
              variants={percentageProgress}
              initial="initial"
              animate="animate"
              custom={percentage}
            />
          </div>
        </div>
      </CardHeader>

      {/* Subcategories */}
      <AnimatePresence>
        {isExpanded && subcategories && Object.keys(subcategories).length > 0 && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <CardContent className="pt-0 pb-4">
              <div className="space-y-3">
                {Object.entries(subcategories).map(([subcat, subAmount]) => (
                  <div key={subcat} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-0">
                    <span className="text-sm text-gray-700 capitalize">
                      {subcat.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                    <div className="text-right">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(subAmount)}
                      </span>
                      <div className="text-xs text-gray-500">
                        {((subAmount / totalCost) * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
    </motion.div>
  );
}

export function CostBreakdownCard({ 
  breakdown, 
  title, 
  amount, 
  percentage, 
  subcategories, 
  totalCost 
}: CostBreakdownCardProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile on component mount
  useEffect(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    hapticFeedback.light();
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  // If individual props are provided (mobile single category mode)
  if (title && amount !== undefined && percentage !== undefined && totalCost !== undefined) {
    return (
      <SingleCategoryCard
        title={title}
        amount={amount}
        percentage={percentage}
        subcategories={subcategories}
        totalCost={totalCost}
      />
    );
  }

  // If breakdown is not provided, return null
  if (!breakdown) {
    return null;
  }

  const categories = Object.entries(breakdown).filter(([key]) => key !== 'total');
  const staggerAnimation = createAdvancedStagger(categories.length, 0.1, 0.1);

  return (
    <motion.div 
      className="space-y-4"
      variants={staggerAnimation.container}
      initial="initial"
      animate="animate"
    >
      {categories.map(([category, data], index) => {
        const categoryInfo = categoryData[category];
        const isExpanded = expandedCategories.has(category);

        return (
          <motion.div
            key={category}
            variants={staggerAnimation.item}
            {...createHoverAnimation(1.02, 0, '0 8px 30px rgba(0, 0, 0, 0.12)')}
          >
            <Card className="overflow-hidden">
              <CardHeader
                className={cn(
                  'cursor-pointer hover:bg-gray-50 transition-colors',
                  isMobile && mobileClasses.touchTarget
                )}
                onClick={() => toggleCategory(category)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{categoryInfo?.icon}</div>
                    <div>
                      <CardTitle className="text-lg">
                        {categoryInfo?.name || category}
                      </CardTitle>
                      <p className="text-sm text-gray-600 mt-1">
                        {categoryInfo?.description}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <motion.div 
                        className="text-2xl font-bold text-gray-900"
                        variants={enhancedCountUp}
                        initial="initial"
                        animate="animate"
                      >
                        {formatCurrency(data.amount)}
                      </motion.div>
                      <div className="text-sm text-gray-500">
                        {data.percentage}% of total
                      </div>
                    </div>

                    <motion.div
                      animate={{ rotate: isExpanded ? 90 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    </motion.div>
                  </div>
                </div>
              </CardHeader>

              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <CardContent className="pt-0 border-t bg-gray-50">
                      {data.subCategories && data.subCategories.length > 0 ? (
                        <div className="space-y-3">
                          <h4 className="font-medium text-gray-900 flex items-center gap-2">
                            <Info className="h-4 w-4" />
                            Detailed Breakdown
                          </h4>

                          {data.subCategories.map((subCategory: any, subIndex: number) => (
                            <motion.div
                              key={`${category}-${subIndex}`}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.3, delay: subIndex * 0.05 }}
                              className="flex justify-between items-center py-2 px-3 bg-white rounded border"
                            >
                              <div>
                                <div className="font-medium text-gray-900">
                                  {subCategory.name}
                                </div>
                                {subCategory.description && (
                                  <div className="text-sm text-gray-600">
                                    {subCategory.description}
                                  </div>
                                )}
                              </div>

                              <div className="text-right">
                                <div className="font-semibold text-gray-900">
                                  {formatCurrency(subCategory.amount)}
                                </div>
                                {subCategory.percentage && (
                                  <div className="text-xs text-gray-500">
                                    {subCategory.percentage.toFixed(1)}%
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      ) : (
                        <div className="py-4 text-center text-gray-500">
                          <Info className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                          <p className="text-sm">
                            Detailed breakdown will be available in the full report
                          </p>
                        </div>
                      )}

                      {/* Progress bar */}
                      <div className="mt-4 pt-4 border-t">
                        <div className="flex justify-between text-sm mb-2">
                          <span className="text-gray-600">
                            Share of total project cost
                          </span>
                          <span className="font-medium">
                            {data.percentage}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                          <motion.div
                            className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full"
                            variants={percentageProgress}
                            initial="initial"
                            animate="animate"
                            custom={data.percentage}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </motion.div>
                )}
              </AnimatePresence>
            </Card>
          </motion.div>
        );
      })}

      {/* Total summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: categories.length * 0.1 }}
      >
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  Total Project Cost
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Including all construction components
                </p>
              </div>
              <motion.div 
                className="text-3xl font-bold text-blue-600"
                variants={enhancedCountUp}
                initial="initial"
                animate="animate"
              >
                {formatCurrency(breakdown.total)}
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}