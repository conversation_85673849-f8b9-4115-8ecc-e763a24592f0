/**
 * Mobile UX Enhancement Library
 * Centralized exports for all mobile-related functionality
 */

// Core mobile handlers
export { default as TouchHandler } from './touch-handler';
export type {
  TouchEventData,
  SwipeGesture,
  TouchHandlerOptions,
} from './touch-handler';

// Gesture navigation
export { default as GestureNavigation } from './gesture-navigation';
export type {
  GestureNavigationOptions,
  NavigationState,
} from './gesture-navigation';

// Haptic feedback
export { default as hapticFeedback, HapticFeedback } from './haptic-feedback';
export type { HapticPattern, HapticFeedbackOptions } from './haptic-feedback';

// PWA management
export { default as pwaManager, PWAManager } from './pwa-manager';
export type { PWAConfig, BeforeInstallPromptEvent } from './pwa-manager';

// Offline functionality
export { default as offlineManager, OfflineManager } from './offline-manager';
export type { OfflineData, OfflineConfig } from './offline-manager';

// Performance optimization
export {
  default as mobilePerformanceOptimizer,
  MobilePerformanceOptimizer,
} from './performance-optimizer';
export type {
  PerformanceConfig,
  PerformanceMetrics,
} from './performance-optimizer';

// Utilities
export { touchUtils } from './touch-handler';

// Mobile-specific constants
export const MOBILE_CONSTANTS = {
  MIN_TOUCH_TARGET: 44, // iOS HIG minimum
  SWIPE_THRESHOLD: 50,
  LONG_PRESS_DELAY: 500,
  HAPTIC_THROTTLE: 50,
  VIEWPORT_BREAKPOINTS: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },
  SAFE_AREA_INSETS: {
    top: 'env(safe-area-inset-top)',
    bottom: 'env(safe-area-inset-bottom)',
    left: 'env(safe-area-inset-left)',
    right: 'env(safe-area-inset-right)',
  },
};

// Mobile detection utilities
export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
};

export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

export const isIOSDevice = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
};

export const isAndroidDevice = (): boolean => {
  return /Android/.test(navigator.userAgent);
};

export const getDevicePixelRatio = (): number => {
  return window.devicePixelRatio || 1;
};

export const getViewportSize = (): { width: number; height: number } => {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
};

export const getOrientation = (): 'portrait' | 'landscape' => {
  return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
};

// Mobile input utilities
export const getMobileInputType = (fieldType: string): string => {
  const typeMap: Record<string, string> = {
    number: 'number',
    email: 'email',
    phone: 'tel',
    url: 'url',
    area: 'number',
    cost: 'number',
    text: 'text',
  };
  return typeMap[fieldType] || 'text';
};

export const mobileClasses = {
  input: 'text-base min-h-[44px] touch-manipulation',
  button: 'min-h-[44px] min-w-[44px] touch-manipulation',
  touchTarget: 'min-h-[44px] min-w-[44px]',
};

export const preventZoomOnFocus = (): void => {
  if (isIOSDevice()) {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute(
        'content',
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      );
    }
  }
};

export const restoreZoom = (): void => {
  if (isIOSDevice()) {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute(
        'content',
        'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes'
      );
    }
  }
};

export const isMobileViewport = (): boolean => {
  return window.innerWidth <= MOBILE_CONSTANTS.VIEWPORT_BREAKPOINTS.mobile;
};

// Network detection
export const getNetworkInfo = (): {
  type?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
} => {
  const connection =
    (navigator as any).connection ||
    (navigator as any).mozConnection ||
    (navigator as any).webkitConnection;

  if (connection) {
    return {
      type: connection.type,
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
    };
  }

  return {};
};

// Battery API
export const getBatteryInfo = async (): Promise<{
  level?: number;
  charging?: boolean;
  chargingTime?: number;
  dischargingTime?: number;
} | null> => {
  try {
    if ('getBattery' in navigator) {
      const battery = await (navigator as any).getBattery();
      return {
        level: battery.level,
        charging: battery.charging,
        chargingTime: battery.chargingTime,
        dischargingTime: battery.dischargingTime,
      };
    }
  } catch (error) {
    console.warn('Battery API not available:', error);
  }

  return null;
};

// Initialize mobile optimizations
export const initializeMobileUX = async (
  options: {
    enableHaptic?: boolean;
    enableGestures?: boolean;
    enablePWA?: boolean;
    enableOffline?: boolean;
    enablePerformanceOptimization?: boolean;
  } = {}
) => {
  const {
    enableHaptic = true,
    enableGestures = true,
    enablePWA = true,
    enableOffline = true,
    enablePerformanceOptimization = true,
  } = options;

  console.log('Initializing Mobile UX enhancements...');

  // Initialize performance optimization first
  if (enablePerformanceOptimization) {
    mobilePerformanceOptimizer.forceOptimization('images');
    mobilePerformanceOptimizer.forceOptimization('lazy');
  }

  // Setup haptic feedback
  if (enableHaptic && isTouchDevice()) {
    hapticFeedback.enable();

    // Auto-enhance common elements
    hapticFeedback.enhanceElements('button:not(.no-haptic)', 'button');
    hapticFeedback.enhanceElements(
      'input[type="submit"]:not(.no-haptic)',
      'button'
    );
  }

  // Setup PWA features
  if (enablePWA) {
    // PWA manager is initialized automatically
    console.log('PWA features enabled');
  }

  // Setup offline functionality
  if (enableOffline) {
    // Offline manager is initialized automatically
    console.log('Offline functionality enabled');
  }

  // Apply mobile-specific optimizations
  if (isMobileDevice()) {
    // Prevent iOS bounce scroll
    document.body.style.overscrollBehavior = 'none';

    // Optimize touch scrolling
    document.body.style.webkitOverflowScrolling = 'touch';

    // Add mobile CSS classes
    document.documentElement.classList.add('mobile-device');
  }

  if (isTouchDevice()) {
    document.documentElement.classList.add('touch-device');
  }

  // Setup viewport meta tag for mobile
  let viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    viewport = document.createElement('meta');
    viewport.setAttribute('name', 'viewport');
    document.head.appendChild(viewport);
  }

  viewport.setAttribute(
    'content',
    'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes'
  );

  console.log('Mobile UX initialization complete');
};

// Global CSS for mobile optimizations
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    .touch-device {
      /* Touch-specific styles */
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }

    .mobile-device {
      /* Mobile-specific styles */
      -webkit-text-size-adjust: 100%;
    }

    .touch-optimized {
      min-width: 44px;
      min-height: 44px;
      touch-action: manipulation;
      user-select: none;
    }

    .safe-area-padding {
      padding-top: env(safe-area-inset-top);
      padding-bottom: env(safe-area-inset-bottom);
      padding-left: env(safe-area-inset-left);
      padding-right: env(safe-area-inset-right);
    }

    .power-saving-mode * {
      animation-duration: 0.1s !important;
      transition-duration: 0.1s !important;
    }

    .pwa-installed {
      /* PWA-specific styles when app is installed */
    }

    @media (pointer: coarse) {
      /* Styles for touch devices */
      button, [role="button"] {
        min-height: 44px;
        min-width: 44px;
      }

      input, textarea, select {
        font-size: 16px; /* Prevent zoom on iOS */
      }
    }

    @media (max-width: 768px) {
      /* Mobile-specific responsive styles */
      .container {
        padding-left: 16px;
        padding-right: 16px;
      }
    }
  `;

  document.head.appendChild(style);
}
