/**
 * Comprehensive Day 1-5 Testing Suite
 * Automated UI testing for complete Enhanced MVP
 */

import { test, expect, Page } from '@playwright/test';

test.describe('Day 1-5 Comprehensive Testing Suite', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    await page.goto('/');
  });

  test.afterEach(async () => {
    await page.close();
  });

  test.describe('Day 1: UI Revolution Tests', () => {
    test('should load homepage with modern design system', async () => {
      // Test page loads
      await expect(page).toHaveTitle(/Nirmaan AI/);
      
      // Test design system components are present
      await expect(page.locator('[data-testid="hero-section"]')).toBeVisible();
      await expect(page.locator('[data-testid="cta-button"]')).toBeVisible();
      
      // Test animations are working (check for framer-motion classes)
      const animatedElement = page.locator('.motion-div').first();
      await expect(animatedElement).toBeVisible();
    });

    test('should have responsive design working', async () => {
      // Test desktop view
      await page.setViewportSize({ width: 1200, height: 800 });
      await expect(page.locator('[data-testid="desktop-nav"]')).toBeVisible();
      
      // Test mobile view
      await page.setViewportSize({ width: 375, height: 667 });
      await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    });

    test('should have enhanced components with proper styling', async () => {
      // Test enhanced buttons
      const primaryButton = page.locator('button').filter({ hasText: 'Start Calculating' }).first();
      await expect(primaryButton).toBeVisible();
      await expect(primaryButton).toHaveClass(/bg-primary/);
      
      // Test enhanced cards
      const cards = page.locator('[data-testid="feature-card"]');
      await expect(cards).toHaveCount(3);
    });
  });

  test.describe('Day 2: Smart Form Wizard Tests', () => {
    test('should navigate to calculator and load form wizard', async () => {
      // Navigate to calculator
      await page.click('text=Start Calculating');
      await expect(page).toHaveURL(/\/calculator/);
      
      // Check wizard is loaded
      await expect(page.locator('[data-testid="wizard-progress"]')).toBeVisible();
      await expect(page.locator('[data-testid="step-1"]')).toBeVisible();
    });

    test('should complete Step 1: Basic Info with validation', async () => {
      await page.goto('/calculator');
      
      // Fill basic info
      await page.fill('[data-testid="plot-size-input"]', '2000');
      await page.fill('[data-testid="built-up-area-input"]', '1500');
      await page.selectOption('[data-testid="floors-select"]', '2');
      await page.selectOption('[data-testid="location-select"]', 'bangalore');
      await page.selectOption('[data-testid="building-type-select"]', 'residential');
      
      // Check validation passes and next button is enabled
      const nextButton = page.locator('[data-testid="next-button"]');
      await expect(nextButton).toBeEnabled();
      
      // Click next to proceed
      await nextButton.click();
      
      // Verify we moved to step 2
      await expect(page.locator('[data-testid="step-2"]')).toBeVisible();
    });

    test('should complete Step 2: Room Configuration', async () => {
      await page.goto('/calculator');
      
      // Complete step 1 first
      await page.fill('[data-testid="plot-size-input"]', '2000');
      await page.selectOption('[data-testid="floors-select"]', '2');
      await page.selectOption('[data-testid="location-select"]', 'bangalore');
      await page.selectOption('[data-testid="building-type-select"]', 'residential');
      await page.click('[data-testid="next-button"]');
      
      // Configure rooms
      await page.selectOption('[data-testid="bedrooms-select"]', '3');
      await page.selectOption('[data-testid="bathrooms-select"]', '2');
      await page.selectOption('[data-testid="kitchens-select"]', '1');
      
      // Proceed to step 3
      await page.click('[data-testid="next-button"]');
      await expect(page.locator('[data-testid="step-3"]')).toBeVisible();
    });

    test('should complete Step 3: Quality Selection', async () => {
      await page.goto('/calculator');
      
      // Complete previous steps
      await page.fill('[data-testid="plot-size-input"]', '2000');
      await page.selectOption('[data-testid="floors-select"]', '2');
      await page.selectOption('[data-testid="location-select"]', 'bangalore');
      await page.selectOption('[data-testid="building-type-select"]', 'residential');
      await page.click('[data-testid="next-button"]');
      
      await page.selectOption('[data-testid="bedrooms-select"]', '3');
      await page.selectOption('[data-testid="bathrooms-select"]', '2');
      await page.click('[data-testid="next-button"]');
      
      // Select quality tier
      await page.click('[data-testid="quality-premium"]');
      
      // Select materials
      await page.click('[data-testid="flooring-ceramic"]');
      await page.click('[data-testid="wall-paint"]');
      
      // Proceed to step 4
      await page.click('[data-testid="next-button"]');
      await expect(page.locator('[data-testid="step-4"]')).toBeVisible();
    });

    test('should complete Step 4: Advanced Features and calculate', async () => {
      await page.goto('/calculator');
      
      // Complete all previous steps quickly
      await page.fill('[data-testid="plot-size-input"]', '2000');
      await page.selectOption('[data-testid="floors-select"]', '2');
      await page.selectOption('[data-testid="location-select"]', 'bangalore');
      await page.selectOption('[data-testid="building-type-select"]', 'residential');
      await page.click('[data-testid="next-button"]');
      
      await page.selectOption('[data-testid="bedrooms-select"]', '3');
      await page.selectOption('[data-testid="bathrooms-select"]', '2');
      await page.click('[data-testid="next-button"]');
      
      await page.click('[data-testid="quality-premium"]');
      await page.click('[data-testid="next-button"]');
      
      // Select advanced features
      await page.check('[data-testid="solar-panels"]');
      await page.check('[data-testid="home-automation"]');
      
      // Calculate
      await page.click('[data-testid="calculate-button"]');
      
      // Wait for results
      await expect(page.locator('[data-testid="results-display"]')).toBeVisible({ timeout: 10000 });
    });

    test('should show smart defaults and recommendations', async () => {
      await page.goto('/calculator');
      
      // Fill plot size and check for smart defaults
      await page.fill('[data-testid="plot-size-input"]', '2000');
      
      // Check if built-up area is auto-suggested
      const builtUpArea = page.locator('[data-testid="built-up-area-input"]');
      await expect(builtUpArea).toHaveValue(/1200|1400|1600/); // Should suggest reasonable built-up area
      
      // Check for recommendations
      await expect(page.locator('[data-testid="smart-recommendation"]')).toBeVisible();
    });
  });

  test.describe('Day 3: Results Enhancement Tests', () => {
    test('should display enhanced results with charts', async () => {
      // Complete calculation flow
      await page.goto('/calculator');
      await page.fill('[data-testid="plot-size-input"]', '2000');
      await page.selectOption('[data-testid="floors-select"]', '2');
      await page.selectOption('[data-testid="location-select"]', 'bangalore');
      await page.selectOption('[data-testid="building-type-select"]', 'residential');
      await page.click('[data-testid="next-button"]');
      
      await page.selectOption('[data-testid="bedrooms-select"]', '3');
      await page.click('[data-testid="next-button"]');
      
      await page.click('[data-testid="quality-premium"]');
      await page.click('[data-testid="next-button"]');
      
      await page.click('[data-testid="calculate-button"]');
      
      // Check results display
      await expect(page.locator('[data-testid="results-display"]')).toBeVisible({ timeout: 10000 });
      await expect(page.locator('[data-testid="total-cost"]')).toBeVisible();
      await expect(page.locator('[data-testid="cost-breakdown"]')).toBeVisible();
    });

    test('should show interactive charts', async () => {
      // Navigate to results (assuming we have a direct route for testing)
      await page.goto('/results?test=true');
      
      // Check for interactive charts
      await expect(page.locator('[data-testid="pie-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="bar-chart"]')).toBeVisible();
      
      // Test chart interactions
      await page.click('[data-testid="chart-type-comparison"]');
      await expect(page.locator('[data-testid="comparison-chart"]')).toBeVisible();
    });

    test('should generate and download PDF report', async () => {
      await page.goto('/results?test=true');
      
      // Test PDF download
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="download-pdf-button"]');
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toMatch(/construction-report.*\.pdf/);
    });
  });

  test.describe('Day 4: User System Tests', () => {
    test('should show authentication system', async () => {
      await page.goto('/auth/login');
      
      // Check auth form is present
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
      await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
      
      // Check social login options
      await expect(page.locator('[data-testid="google-login"]')).toBeVisible();
      await expect(page.locator('[data-testid="phone-login"]')).toBeVisible();
    });

    test('should handle login flow', async () => {
      await page.goto('/auth/login');
      
      // Fill login form
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'password123');
      
      // Submit form
      await page.click('[data-testid="login-button"]');
      
      // Should redirect to dashboard
      await expect(page).toHaveURL(/\/dashboard/);
    });

    test('should show user dashboard', async () => {
      // Mock login state
      await page.goto('/dashboard');
      
      // Check dashboard elements
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="stats-cards"]')).toBeVisible();
      await expect(page.locator('[data-testid="projects-grid"]')).toBeVisible();
      
      // Check project management features
      await expect(page.locator('[data-testid="new-project-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="search-projects"]')).toBeVisible();
    });

    test('should handle project management', async () => {
      await page.goto('/dashboard');
      
      // Test project creation
      await page.click('[data-testid="new-project-button"]');
      await expect(page).toHaveURL(/\/calculator/);
      
      // Go back to dashboard
      await page.goto('/dashboard');
      
      // Test project search
      await page.fill('[data-testid="search-projects"]', 'villa');
      await expect(page.locator('[data-testid="project-card"]')).toBeVisible();
    });
  });

  test.describe('Day 5: Admin Panel Tests', () => {
    test('should access admin panel with proper authentication', async () => {
      // Mock admin authentication
      await page.goto('/admin');
      
      // Check admin layout
      await expect(page.locator('[data-testid="admin-sidebar"]')).toBeVisible();
      await expect(page.locator('[data-testid="admin-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="admin-dashboard"]')).toBeVisible();
    });

    test('should show admin dashboard with statistics', async () => {
      await page.goto('/admin');
      
      // Check stats cards
      await expect(page.locator('[data-testid="total-users-stat"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-projects-stat"]')).toBeVisible();
      await expect(page.locator('[data-testid="revenue-stat"]')).toBeVisible();
      await expect(page.locator('[data-testid="materials-stat"]')).toBeVisible();
      
      // Check recent activity
      await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-users"]')).toBeVisible();
    });

    test('should navigate admin sections', async () => {
      await page.goto('/admin');
      
      // Test navigation to materials
      await page.click('[data-testid="nav-materials"]');
      await expect(page).toHaveURL(/\/admin\/materials/);
      
      // Test navigation to users
      await page.click('[data-testid="nav-users"]');
      await expect(page).toHaveURL(/\/admin\/users/);
      
      // Test navigation to analytics
      await page.click('[data-testid="nav-analytics"]');
      await expect(page).toHaveURL(/\/admin\/analytics/);
    });

    test('should handle admin quick actions', async () => {
      await page.goto('/admin');
      
      // Test quick actions
      await expect(page.locator('[data-testid="quick-actions"]')).toBeVisible();
      
      // Test refresh functionality
      await page.click('[data-testid="refresh-button"]');
      await expect(page.locator('[data-testid="loading-indicator"]')).toBeVisible();
      
      // Test export functionality
      await page.click('[data-testid="export-button"]');
      // Should trigger download or show export modal
    });
  });

  test.describe('Cross-Day Integration Tests', () => {
    test('should maintain state across wizard steps', async () => {
      await page.goto('/calculator');
      
      // Fill step 1
      await page.fill('[data-testid="plot-size-input"]', '2500');
      await page.selectOption('[data-testid="location-select"]', 'mumbai');
      await page.click('[data-testid="next-button"]');
      
      // Go to step 3
      await page.click('[data-testid="next-button"]');
      
      // Go back to step 1
      await page.click('[data-testid="back-button"]');
      await page.click('[data-testid="back-button"]');
      
      // Verify data is preserved
      await expect(page.locator('[data-testid="plot-size-input"]')).toHaveValue('2500');
      await expect(page.locator('[data-testid="location-select"]')).toHaveValue('mumbai');
    });

    test('should handle complete user journey', async () => {
      // Start from homepage
      await page.goto('/');
      
      // Navigate to calculator
      await page.click('text=Start Calculating');
      
      // Complete calculation
      await page.fill('[data-testid="plot-size-input"]', '3000');
      await page.selectOption('[data-testid="floors-select"]', '3');
      await page.selectOption('[data-testid="location-select"]', 'delhi');
      await page.selectOption('[data-testid="building-type-select"]', 'residential');
      await page.click('[data-testid="next-button"]');
      
      await page.selectOption('[data-testid="bedrooms-select"]', '4');
      await page.selectOption('[data-testid="bathrooms-select"]', '3');
      await page.click('[data-testid="next-button"]');
      
      await page.click('[data-testid="quality-luxury"]');
      await page.click('[data-testid="next-button"]');
      
      await page.check('[data-testid="solar-panels"]');
      await page.click('[data-testid="calculate-button"]');
      
      // View results
      await expect(page.locator('[data-testid="results-display"]')).toBeVisible({ timeout: 15000 });
      
      // Save project (should prompt for login)
      await page.click('[data-testid="save-project-button"]');
      
      // Should redirect to auth or show auth modal
      await expect(page.locator('[data-testid="auth-form"]')).toBeVisible();
    });

    test('should handle error states gracefully', async () => {
      await page.goto('/calculator');
      
      // Try to proceed without filling required fields
      await page.click('[data-testid="next-button"]');
      
      // Should show validation errors
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      
      // Fill minimum required and proceed
      await page.fill('[data-testid="plot-size-input"]', '1000');
      await page.selectOption('[data-testid="floors-select"]', '1');
      await page.selectOption('[data-testid="location-select"]', 'bangalore');
      await page.selectOption('[data-testid="building-type-select"]', 'residential');
      
      // Should now be able to proceed
      await page.click('[data-testid="next-button"]');
      await expect(page.locator('[data-testid="step-2"]')).toBeVisible();
    });
  });

  test.describe('Performance and Accessibility Tests', () => {
    test('should load pages within performance budget', async () => {
      const startTime = Date.now();
      await page.goto('/');
      const loadTime = Date.now() - startTime;
      
      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    test('should be accessible', async () => {
      await page.goto('/');
      
      // Check for proper heading structure
      await expect(page.locator('h1')).toBeVisible();
      
      // Check for alt text on images
      const images = page.locator('img');
      const imageCount = await images.count();
      
      for (let i = 0; i < imageCount; i++) {
        const img = images.nth(i);
        await expect(img).toHaveAttribute('alt');
      }
      
      // Check for proper form labels
      await page.goto('/calculator');
      const inputs = page.locator('input[type="text"], input[type="email"], select');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const id = await input.getAttribute('id');
        if (id) {
          await expect(page.locator(`label[for="${id}"]`)).toBeVisible();
        }
      }
    });
  });
});
