/**
 * Environment Configuration
 * Centralized environment variable management with validation
 */

// App Configuration
export const appConfig = {
  name: process.env.NEXT_PUBLIC_APP_NAME || 'Nirmaan AI Calculator',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  description: 'AI-powered construction cost calculator for India',
  version: '1.0.0',
  environment: process.env.NODE_ENV || 'development',
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',
};

// Supabase Configuration
export const supabaseConfig = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
};

// Analytics Configuration
export const analyticsConfig = {
  googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
  posthogApiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY,
  posthogHost: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
  hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
  mixpanelToken: process.env.NEXT_PUBLIC_MIXPANEL_TOKEN,
};

// Feature Flags
export const featureFlags = {
  enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  enablePerformanceMonitoring: process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING === 'true',
  enableErrorReporting: process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING === 'true',
  enableA11yTesting: process.env.NEXT_PUBLIC_ENABLE_A11Y_TESTING === 'true',
  enableDebugMode: process.env.NEXT_PUBLIC_DEBUG_MODE === 'true',
  enableMockData: process.env.NEXT_PUBLIC_ENABLE_MOCK_DATA === 'true',
  enableBetaFeatures: process.env.NEXT_PUBLIC_ENABLE_BETA_FEATURES === 'true',
};

// API Configuration
export const apiConfig = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || appConfig.url,
  timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000'),
  retryAttempts: parseInt(process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS || '3'),
  retryDelay: parseInt(process.env.NEXT_PUBLIC_API_RETRY_DELAY || '1000'),
};

// Database Configuration
export const dbConfig = {
  maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
  connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
  queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '10000'),
};

// Cache Configuration
export const cacheConfig = {
  defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL || '300'), // 5 minutes
  maxTtl: parseInt(process.env.CACHE_MAX_TTL || '3600'), // 1 hour
  redisUrl: process.env.REDIS_URL,
  enableRedis: !!process.env.REDIS_URL,
};

// Security Configuration
export const securityConfig = {
  jwtSecret: process.env.JWT_SECRET || 'fallback-secret-for-development',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
  corsOrigins: process.env.CORS_ORIGINS?.split(',') || [appConfig.url],
  rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'),
  rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
};

// Email Configuration
export const emailConfig = {
  provider: process.env.EMAIL_PROVIDER || 'resend',
  apiKey: process.env.EMAIL_API_KEY,
  fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
  fromName: process.env.EMAIL_FROM_NAME || 'Nirmaan AI',
  replyTo: process.env.EMAIL_REPLY_TO,
};

// Storage Configuration
export const storageConfig = {
  provider: process.env.STORAGE_PROVIDER || 'supabase',
  bucket: process.env.STORAGE_BUCKET || 'uploads',
  maxFileSize: parseInt(process.env.STORAGE_MAX_FILE_SIZE || '10485760'), // 10MB
  allowedTypes: process.env.STORAGE_ALLOWED_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/webp',
    'application/pdf'
  ],
};

// Payment Configuration
export const paymentConfig = {
  provider: process.env.PAYMENT_PROVIDER || 'stripe',
  publicKey: process.env.NEXT_PUBLIC_PAYMENT_PUBLIC_KEY,
  secretKey: process.env.PAYMENT_SECRET_KEY,
  webhookSecret: process.env.PAYMENT_WEBHOOK_SECRET,
  currency: process.env.PAYMENT_CURRENCY || 'INR',
};

// Monitoring Configuration
export const monitoringConfig = {
  sentryDsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  logLevel: process.env.LOG_LEVEL || 'info',
  enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING === 'true',
  enableQueryLogging: process.env.ENABLE_QUERY_LOGGING === 'true',
};

// Helper functions
export const isAnalyticsEnabled = () => {
  return featureFlags.enableAnalytics && appConfig.isProduction;
};

export const isPerformanceMonitoringEnabled = () => {
  return featureFlags.enablePerformanceMonitoring;
};

export const isErrorReportingEnabled = () => {
  return featureFlags.enableErrorReporting && appConfig.isProduction;
};

export const isDebugMode = () => {
  return featureFlags.enableDebugMode || appConfig.isDevelopment;
};

// Validation functions
export const validateRequiredEnvVars = () => {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};

// Environment validation
if (appConfig.isProduction) {
  validateRequiredEnvVars();
}

// Export all configurations
export default {
  app: appConfig,
  supabase: supabaseConfig,
  analytics: analyticsConfig,
  features: featureFlags,
  api: apiConfig,
  db: dbConfig,
  cache: cacheConfig,
  security: securityConfig,
  email: emailConfig,
  storage: storageConfig,
  payment: paymentConfig,
  monitoring: monitoringConfig,
  helpers: {
    isAnalyticsEnabled,
    isPerformanceMonitoringEnabled,
    isErrorReportingEnabled,
    isDebugMode,
    validateRequiredEnvVars,
  },
};
