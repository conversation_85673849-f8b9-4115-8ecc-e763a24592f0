/**
 * Enhanced Authentication System
 * Day 4 Task 1: Beautiful Authentication System (2 hours)
 * Modern auth with social login, security features, and smooth UX
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, 
  EyeOff, 
  Mail, 
  Lock, 
  User, 
  Phone,
  Shield,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowRight,
  X
} from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { MobileInput } from '@/components/ui/mobile-input';
import { cn } from '@/lib/utils';

interface AuthFormData {
  email: string;
  password: string;
  confirmPassword?: string;
  name?: string;
  phone?: string;
  rememberMe?: boolean;
  acceptTerms?: boolean;
}

interface AuthSystemProps {
  mode?: 'login' | 'signup' | 'modal';
  onSuccess?: (user: any) => void;
  onClose?: () => void;
  redirectTo?: string;
  className?: string;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
  label: string;
}

// Password strength validation
const validatePasswordStrength = (password: string): PasswordStrength => {
  let score = 0;
  const feedback: string[] = [];
  
  if (password.length >= 8) score += 1;
  else feedback.push('At least 8 characters');
  
  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('One uppercase letter');
  
  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('One lowercase letter');
  
  if (/\d/.test(password)) score += 1;
  else feedback.push('One number');
  
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
  else feedback.push('One special character');
  
  const strengthMap = {
    0: { color: 'bg-red-500', label: 'Very Weak' },
    1: { color: 'bg-red-400', label: 'Weak' },
    2: { color: 'bg-yellow-500', label: 'Fair' },
    3: { color: 'bg-yellow-400', label: 'Good' },
    4: { color: 'bg-green-500', label: 'Strong' },
    5: { color: 'bg-green-600', label: 'Very Strong' },
  };
  
  return {
    score,
    feedback,
    color: strengthMap[score as keyof typeof strengthMap].color,
    label: strengthMap[score as keyof typeof strengthMap].label,
  };
};

export function EnhancedAuthSystem({
  mode = 'login',
  onSuccess,
  onClose,
  redirectTo,
  className,
}: AuthSystemProps) {
  const [authMode, setAuthMode] = useState<'login' | 'signup'>(mode === 'modal' ? 'login' : mode);
  const [formData, setFormData] = useState<AuthFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    phone: '',
    rememberMe: false,
    acceptTerms: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength | null>(null);

  // Update password strength when password changes
  useEffect(() => {
    if (authMode === 'signup' && formData.password) {
      setPasswordStrength(validatePasswordStrength(formData.password));
    } else {
      setPasswordStrength(null);
    }
  }, [formData.password, authMode]);

  const updateFormData = (field: keyof AuthFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }

    // Signup specific validations
    if (authMode === 'signup') {
      if (!formData.name) {
        newErrors.name = 'Name is required';
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }

      if (!formData.acceptTerms) {
        newErrors.acceptTerms = 'Please accept the terms and conditions';
      }

      // Strong password requirement for signup
      if (passwordStrength && passwordStrength.score < 3) {
        newErrors.password = 'Please choose a stronger password';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful authentication
      const mockUser = {
        id: '1',
        email: formData.email,
        name: formData.name || 'User',
        avatar: null,
      };
      
      onSuccess?.(mockUser);
      
    } catch (error) {
      setErrors({ general: 'Authentication failed. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = async (provider: 'google' | 'phone') => {
    setIsLoading(true);
    
    try {
      // Simulate social login
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Social User',
        avatar: null,
        provider,
      };
      
      onSuccess?.(mockUser);
      
    } catch (error) {
      setErrors({ general: `${provider} login failed. Please try again.` });
    } finally {
      setIsLoading(false);
    }
  };

  const authVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  };

  return (
    <div className={cn('w-full max-w-md mx-auto', className)}>
      <AnimatePresence mode="wait">
        <motion.div
          key={authMode}
          variants={authVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{ duration: 0.3 }}
        >
          <EnhancedCard variant="default" size="lg" className="relative">
            {/* Close button for modal mode */}
            {mode === 'modal' && onClose && (
              <button
                onClick={onClose}
                className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            )}

            <div className="p-8">
              {/* Header */}
              <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-secondary-900 mb-2">
                  {authMode === 'login' ? 'Welcome Back' : 'Create Account'}
                </h1>
                <p className="text-secondary-600">
                  {authMode === 'login' 
                    ? 'Sign in to access your construction projects'
                    : 'Join thousands of users planning their dream homes'
                  }
                </p>
              </div>

              {/* Social Login Buttons */}
              <div className="space-y-3 mb-6">
                <EnhancedButton
                  variant="outline"
                  size="lg"
                  className="w-full"
                  onClick={() => handleSocialLogin('google')}
                  disabled={isLoading}
                  leftIcon={
                    <svg className="h-5 w-5" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                  }
                >
                  Continue with Google
                </EnhancedButton>

                <EnhancedButton
                  variant="outline"
                  size="lg"
                  className="w-full"
                  onClick={() => handleSocialLogin('phone')}
                  disabled={isLoading}
                  leftIcon={<Phone className="h-5 w-5" />}
                >
                  Continue with Phone
                </EnhancedButton>
              </div>

              {/* Divider */}
              <div className="relative mb-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-secondary-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-secondary-500">Or continue with email</span>
                </div>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* General Error */}
                {errors.general && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700"
                  >
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">{errors.general}</span>
                  </motion.div>
                )}

                {/* Name field (signup only) */}
                {authMode === 'signup' && (
                  <div>
                    <MobileInput
                      type="text"
                      placeholder="Full Name"
                      value={formData.name || ''}
                      onChange={(value) => updateFormData('name', value)}
                      leftIcon={<User className="h-4 w-4" />}
                      error={errors.name}
                      disabled={isLoading}
                    />
                  </div>
                )}

                {/* Email field */}
                <div>
                  <MobileInput
                    type="email"
                    placeholder="Email Address"
                    value={formData.email}
                    onChange={(value) => updateFormData('email', value)}
                    leftIcon={<Mail className="h-4 w-4" />}
                    error={errors.email}
                    disabled={isLoading}
                  />
                </div>

                {/* Password field */}
                <div>
                  <MobileInput
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    value={formData.password}
                    onChange={(value) => updateFormData('password', value)}
                    leftIcon={<Lock className="h-4 w-4" />}
                    rightIcon={
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-secondary-400 hover:text-secondary-600"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    }
                    error={errors.password}
                    disabled={isLoading}
                  />

                  {/* Password Strength Indicator */}
                  {passwordStrength && authMode === 'signup' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-2 space-y-2"
                    >
                      <div className="flex items-center gap-2">
                        <div className="flex-1 bg-secondary-200 rounded-full h-2">
                          <div
                            className={cn('h-2 rounded-full transition-all duration-300', passwordStrength.color)}
                            style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                          />
                        </div>
                        <span className="text-xs text-secondary-600">{passwordStrength.label}</span>
                      </div>
                      {passwordStrength.feedback.length > 0 && (
                        <div className="text-xs text-secondary-600">
                          Missing: {passwordStrength.feedback.join(', ')}
                        </div>
                      )}
                    </motion.div>
                  )}
                </div>

                {/* Confirm Password field (signup only) */}
                {authMode === 'signup' && (
                  <div>
                    <MobileInput
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="Confirm Password"
                      value={formData.confirmPassword || ''}
                      onChange={(value) => updateFormData('confirmPassword', value)}
                      leftIcon={<Lock className="h-4 w-4" />}
                      rightIcon={
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="text-secondary-400 hover:text-secondary-600"
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      }
                      error={errors.confirmPassword}
                      disabled={isLoading}
                    />
                  </div>
                )}

                {/* Remember Me / Accept Terms */}
                <div className="space-y-3">
                  {authMode === 'login' && (
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.rememberMe}
                        onChange={(e) => updateFormData('rememberMe', e.target.checked)}
                        className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                        disabled={isLoading}
                      />
                      <span className="text-sm text-secondary-600">Remember me</span>
                    </label>
                  )}

                  {authMode === 'signup' && (
                    <div>
                      <label className="flex items-start gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.acceptTerms}
                          onChange={(e) => updateFormData('acceptTerms', e.target.checked)}
                          className="mt-0.5 rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                          disabled={isLoading}
                        />
                        <span className="text-sm text-secondary-600">
                          I agree to the{' '}
                          <a href="/terms" className="text-primary-600 hover:underline">
                            Terms of Service
                          </a>{' '}
                          and{' '}
                          <a href="/privacy" className="text-primary-600 hover:underline">
                            Privacy Policy
                          </a>
                        </span>
                      </label>
                      {errors.acceptTerms && (
                        <p className="text-sm text-red-600 mt-1">{errors.acceptTerms}</p>
                      )}
                    </div>
                  )}
                </div>

                {/* Submit Button */}
                <EnhancedButton
                  type="submit"
                  variant="default"
                  size="lg"
                  className="w-full"
                  disabled={isLoading}
                  rightIcon={isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <ArrowRight className="h-4 w-4" />}
                >
                  {isLoading 
                    ? (authMode === 'login' ? 'Signing In...' : 'Creating Account...')
                    : (authMode === 'login' ? 'Sign In' : 'Create Account')
                  }
                </EnhancedButton>
              </form>

              {/* Footer */}
              <div className="mt-6 text-center">
                <p className="text-sm text-secondary-600">
                  {authMode === 'login' ? "Don't have an account?" : 'Already have an account?'}{' '}
                  <button
                    type="button"
                    onClick={() => setAuthMode(authMode === 'login' ? 'signup' : 'login')}
                    className="text-primary-600 hover:underline font-medium"
                    disabled={isLoading}
                  >
                    {authMode === 'login' ? 'Sign up' : 'Sign in'}
                  </button>
                </p>

                {authMode === 'login' && (
                  <p className="text-sm text-secondary-600 mt-2">
                    <a href="/forgot-password" className="text-primary-600 hover:underline">
                      Forgot your password?
                    </a>
                  </p>
                )}
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

export default EnhancedAuthSystem;
