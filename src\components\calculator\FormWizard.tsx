/**
 * Form Wizard Component
 * TurboTax-like guided experience for construction cost calculation
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, type Variants } from 'framer-motion';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { CalculatorErrorBoundary } from '@/components/ui/error-boundary';
import { LoadingSpinner } from '@/components/ui/loading-states';
import { cn } from '@/lib/utils';
import { isMobileViewport } from '@/lib/mobile';
// Accessibility imports removed to fix SSR issues

// Import wizard components
import WizardProgress from './WizardProgress';
import WizardNavigation, { CompactWizardNavigation } from './WizardNavigation';

// Import step components
import BasicInfoStep from './steps/BasicInfoStep';
import RoomConfigStep from './steps/RoomConfigStep';
import QualitySelectionStep from './steps/QualitySelectionStep';
import AdvancedFeaturesStep from './steps/AdvancedFeaturesStep';

// Import types
import {
  CalculatorFormData,
  WizardState,
  WizardActions,
  WizardFormContext,
  StepValidation,
  CalculationResult,
  DEFAULT_FORM_DATA,
  WIZARD_STORAGE_KEYS,
} from './types/wizard';

interface FormWizardProps {
  onResult?: (result: CalculationResult) => void;
  onError?: (error: string) => void;
  className?: string;
}

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
    },
  },
};

const stepVariants: Variants = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -50 },
};

export function FormWizard({ onResult, onError, className }: FormWizardProps) {
  // State management
  const [formData, setFormData] = useState<Partial<CalculatorFormData>>(DEFAULT_FORM_DATA);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [skippedSteps, setSkippedSteps] = useState<Set<number>>(new Set());
  const [stepValidations, setStepValidations] = useState<Record<number, StepValidation>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Simplified accessibility
  const announce = (message: string) => {
    if (typeof window !== 'undefined') {
      console.log('Wizard:', message);
    }
  };

  // Initialize mobile detection
  useEffect(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Define wizard steps
  const steps = useMemo(() => [
    {
      id: 'basic-info',
      title: 'Project Basics',
      description: 'Plot size, floors, location, and building type',
      component: BasicInfoStep,
      fields: ['plotSize', 'builtUpArea', 'floors', 'location', 'buildingType'] as const,
      estimatedTime: '2-3 min',
    },
    {
      id: 'room-config',
      title: 'Room Configuration',
      description: 'Bedrooms, bathrooms, and room layout',
      component: RoomConfigStep,
      fields: ['bedrooms', 'bathrooms', 'kitchens', 'livingRooms', 'studyRooms', 'storeRooms', 'balconies'] as const,
      estimatedTime: '2-3 min',
    },
    {
      id: 'quality-selection',
      title: 'Quality & Finishes',
      description: 'Material quality, finishes, and fixtures',
      component: QualitySelectionStep,
      fields: ['quality', 'flooringType', 'wallFinish', 'ceilingType', 'kitchenType', 'bathroomFixtures', 'electricalFittings'] as const,
      estimatedTime: '3-4 min',
    },
    {
      id: 'advanced-features',
      title: 'Advanced Features',
      description: 'Optional features and customizations',
      component: AdvancedFeaturesStep,
      fields: ['parkingSpaces', 'garden', 'swimmingPool', 'solarPanels', 'rainwaterHarvesting', 'homeAutomation', 'securitySystem', 'elevator', 'generator', 'internetCabling'] as const,
      estimatedTime: '2-3 min',
      optional: true,
    },
  ], []);

  // Load saved data on mount
  useEffect(() => {
    try {
      const savedData = localStorage.getItem(WIZARD_STORAGE_KEYS.FORM_DATA);
      const savedStep = localStorage.getItem(WIZARD_STORAGE_KEYS.CURRENT_STEP);
      const savedCompleted = localStorage.getItem(WIZARD_STORAGE_KEYS.COMPLETED_STEPS);

      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setFormData({ ...DEFAULT_FORM_DATA, ...parsedData });
      }

      if (savedStep) {
        const stepNum = parseInt(savedStep);
        if (stepNum >= 0 && stepNum < steps.length) {
          setCurrentStep(stepNum);
        }
      }

      if (savedCompleted) {
        const completed = JSON.parse(savedCompleted);
        setCompletedSteps(new Set(completed));
      }
    } catch (error) {
      console.warn('Failed to load saved wizard data:', error);
    }
  }, [steps.length]);

  // Save data to localStorage
  const saveToStorage = useCallback(() => {
    try {
      localStorage.setItem(WIZARD_STORAGE_KEYS.FORM_DATA, JSON.stringify(formData));
      localStorage.setItem(WIZARD_STORAGE_KEYS.CURRENT_STEP, currentStep.toString());
      localStorage.setItem(WIZARD_STORAGE_KEYS.COMPLETED_STEPS, JSON.stringify(Array.from(completedSteps)));
      localStorage.setItem(WIZARD_STORAGE_KEYS.LAST_SAVED, new Date().toISOString());
    } catch (error) {
      console.warn('Failed to save wizard data:', error);
    }
  }, [formData, currentStep, completedSteps]);

  // Auto-save on data changes
  useEffect(() => {
    const timeoutId = setTimeout(saveToStorage, 1000);
    return () => clearTimeout(timeoutId);
  }, [saveToStorage]);

  // Calculate wizard state
  const wizardState: WizardState = useMemo(() => {
    const currentValidation = stepValidations[currentStep];
    const isValid = currentValidation?.isValid ?? false;
    const canProceed = isValid || steps[currentStep]?.optional || false;
    const canGoBack = currentStep > 0;
    const progress = ((currentStep + (isValid ? 1 : 0)) / steps.length) * 100;

    return {
      currentStep,
      totalSteps: steps.length,
      completedSteps,
      skippedSteps,
      isValid,
      canProceed,
      canGoBack,
      progress,
    };
  }, [currentStep, stepValidations, completedSteps, skippedSteps, steps]);

  // Form context
  const formContext: WizardFormContext = useMemo(() => ({
    data: formData,
    updateData: (updates: Partial<CalculatorFormData>) => {
      setFormData(prev => ({ ...prev, ...updates }));
    },
    errors,
    setErrors,
    isSubmitting,
    isDirty: Object.keys(formData).length > 0,
  }), [formData, errors, isSubmitting]);

  // Wizard actions
  const wizardActions: WizardActions = useMemo(() => ({
    nextStep: () => {
      if (wizardState.canProceed && currentStep < steps.length - 1) {
        setCompletedSteps(prev => new Set([...prev, currentStep]));
        setCurrentStep(prev => prev + 1);
        announce(`Moved to step ${currentStep + 2}: ${steps[currentStep + 1]?.title}`, 'polite');
      }
    },
    previousStep: () => {
      if (wizardState.canGoBack) {
        setCurrentStep(prev => prev - 1);
        announce(`Moved back to step ${currentStep}: ${steps[currentStep - 1]?.title}`, 'polite');
      }
    },
    goToStep: (step: number) => {
      if (step >= 0 && step < steps.length) {
        setCurrentStep(step);
        announce(`Jumped to step ${step + 1}: ${steps[step]?.title}`, 'polite');
      }
    },
    skipStep: () => {
      if (steps[currentStep]?.optional && currentStep < steps.length - 1) {
        setSkippedSteps(prev => new Set([...prev, currentStep]));
        setCurrentStep(prev => prev + 1);
        announce(`Skipped step ${currentStep + 1}`, 'polite');
      }
    },
    resetWizard: () => {
      setFormData(DEFAULT_FORM_DATA);
      setCurrentStep(0);
      setCompletedSteps(new Set());
      setSkippedSteps(new Set());
      setStepValidations({});
      setErrors({});
      localStorage.removeItem(WIZARD_STORAGE_KEYS.FORM_DATA);
      localStorage.removeItem(WIZARD_STORAGE_KEYS.CURRENT_STEP);
      localStorage.removeItem(WIZARD_STORAGE_KEYS.COMPLETED_STEPS);
      announce('Wizard has been reset', 'polite');
    },
    submitForm: async () => {
      if (!wizardState.canProceed) return;

      setIsSubmitting(true);
      announce('Calculating construction cost...', 'polite');

      try {
        // Simulate API call with delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Transform form data to match API expectations
        const apiData = {
          plotSize: formData.plotSize || '',
          floors: formData.floors || '1',
          quality: formData.quality || 'smart',
          location: formData.location || 'delhi',
          buildingType: formData.buildingType || 'residential',
        };

        const response = await fetch('/api/calculate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiData),
        });

        if (!response.ok) throw new Error('Calculation failed');

        const data = await response.json();
        
        // Calculate additional costs from advanced features
        let additionalCost = 0;
        if (formData.garden) additionalCost += 350000;
        if (formData.swimmingPool) additionalCost += 1150000;
        if (formData.solarPanels) additionalCost += 550000;
        if (formData.rainwaterHarvesting) additionalCost += 200000;
        if (formData.homeAutomation) additionalCost += 400000;
        if (formData.securitySystem) additionalCost += 250000;
        if (formData.internetCabling) additionalCost += 75000;
        if (formData.elevator) additionalCost += 1600000;
        if (formData.generator) additionalCost += 350000;

        const result: CalculationResult = {
          ...data,
          totalCost: data.totalCost + additionalCost,
          builtUpArea: Math.round(
            (parseFloat(formData.builtUpArea || formData.plotSize || '0')) || 
            (parseFloat(formData.plotSize || '0') * 0.6 * parseInt(formData.floors || '1'))
          ),
          quality: formData.quality || 'smart',
          location: formData.location || 'delhi',
        };

        if (onResult) {
          onResult(result);
        }

        announce('Calculation completed successfully', 'polite');
        
        // Clear saved data after successful submission
        localStorage.removeItem(WIZARD_STORAGE_KEYS.FORM_DATA);
        localStorage.removeItem(WIZARD_STORAGE_KEYS.CURRENT_STEP);
        localStorage.removeItem(WIZARD_STORAGE_KEYS.COMPLETED_STEPS);

      } catch (error) {
        console.error('Error calculating costs:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
        
        if (onError) {
          onError(errorMessage);
        }
        
        announce(`Error: ${errorMessage}`, 'assertive');
        
        // For demo purposes, show a sample result with additional costs
        const sampleResult: CalculationResult = {
          totalCost: 2500000,
          costPerSqft: 2000,
          breakdown: {
            structure: 1000000,
            finishing: 800000,
            mep: 400000,
            other: 300000,
          },
          builtUpArea: Math.round(
            (parseFloat(formData.builtUpArea || formData.plotSize || '0')) || 
            (parseFloat(formData.plotSize || '0') * 0.6 * parseInt(formData.floors || '1'))
          ),
          quality: formData.quality || 'smart',
          location: formData.location || 'delhi',
        };

        if (onResult) {
          onResult(sampleResult);
        }
      } finally {
        setIsSubmitting(false);
      }
    },
  }), [wizardState, currentStep, steps, formData, onResult, onError, announce]);

  // Step validation handler
  const handleStepValidation = useCallback((stepIndex: number, validation: StepValidation) => {
    setStepValidations(prev => ({
      ...prev,
      [stepIndex]: validation,
    }));
    setErrors(validation.errors);
  }, []);

  // Current step component
  const CurrentStepComponent = steps[currentStep]?.component;

  return (
    <CalculatorErrorBoundary>
      <motion.div
        className={cn(
          'w-full max-w-6xl mx-auto space-y-6',
          className
        )}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Progress Indicator */}
        <WizardProgress
          state={wizardState}
          steps={steps.map(step => ({
            id: step.id,
            title: step.title,
            description: step.description,
            estimatedTime: step.estimatedTime,
            optional: step.optional,
          }))}
          variant={isMobile ? 'compact' : 'steps'}
          showLabels={!isMobile}
          showDescription={false}
          showTime={!isMobile}
        />

        {/* Step Content */}
        <EnhancedCard variant="default" size="lg" className="min-h-[600px]">
          <AnimatePresence mode="wait">
            {CurrentStepComponent && (
              <motion.div
                key={currentStep}
                variants={stepVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                transition={{ duration: 0.3 }}
              >
                <CurrentStepComponent
                  data={formData}
                  updateData={formContext.updateData}
                  errors={errors}
                  onValidation={(validation) => handleStepValidation(currentStep, validation)}
                  isActive={true}
                  isMobile={isMobile}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Loading Overlay */}
          {isSubmitting && (
            <motion.div
              className="absolute inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <div className="text-center space-y-4">
                <LoadingSpinner size="lg" />
                <div>
                  <h3 className="font-semibold text-lg">Calculating Construction Cost</h3>
                  <p className="text-secondary-600">
                    Processing your project details and generating accurate estimates...
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </EnhancedCard>

        {/* Navigation */}
        {isMobile ? (
          <CompactWizardNavigation
            state={wizardState}
            actions={wizardActions}
            isSubmitting={isSubmitting}
          />
        ) : (
          <WizardNavigation
            state={wizardState}
            actions={wizardActions}
            isSubmitting={isSubmitting}
            allowSkip={true}
            showShortcuts={true}
            showSave={false}
          />
        )}
      </motion.div>
    </CalculatorErrorBoundary>
  );
}

export default FormWizard;