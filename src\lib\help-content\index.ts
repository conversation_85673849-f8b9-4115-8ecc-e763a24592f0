/**
 * Help Content System
 * Provides contextual help and tooltips for form fields
 */

import React from 'react';

export interface HelpContent {
  title: string;
  description: string;
  examples?: string[];
  tips?: string[];
  warnings?: string[];
  links?: Array<{
    text: string;
    url: string;
  }>;
}

export const helpContent: Record<string, HelpContent> = {
  plotSize: {
    title: "Plot Size",
    description: "Total area of your land plot in square feet. This includes the entire property boundary.",
    examples: [
      "30x40 = 1,200 sq ft",
      "40x60 = 2,400 sq ft",
      "50x80 = 4,000 sq ft"
    ],
    tips: [
      "Measure from boundary to boundary",
      "Include any setback areas",
      "Check your property documents for exact measurements"
    ],
    warnings: [
      "Don't confuse with built-up area",
      "Ensure measurements are accurate for precise estimates"
    ]
  },

  builtUpArea: {
    title: "Built-up Area",
    description: "Total covered area of your building including walls, but excluding open areas like balconies and terraces.",
    examples: [
      "2 BHK: 800-1,200 sq ft",
      "3 BHK: 1,200-1,800 sq ft",
      "4 BHK: 1,800-2,500 sq ft"
    ],
    tips: [
      "Usually 60-70% of plot size",
      "Includes wall thickness",
      "Excludes open balconies and terraces"
    ],
    warnings: [
      "Don't include parking area",
      "Different from carpet area"
    ]
  },

  floors: {
    title: "Number of Floors",
    description: "Total floors above ground level. Ground floor is counted as 0, first floor as 1, etc.",
    examples: [
      "Ground only = 0",
      "Ground + 1st = 1",
      "Ground + 1st + 2nd = 2"
    ],
    tips: [
      "Check local building regulations",
      "Consider future expansion needs",
      "Higher floors increase structural costs"
    ],
    warnings: [
      "Additional floors require stronger foundation",
      "Check FSI/FAR limits in your area"
    ]
  },

  location: {
    title: "Construction Location",
    description: "City where you plan to construct. This affects material costs, labor rates, and local regulations.",
    examples: [
      "Bangalore: Premium materials popular",
      "Mumbai: Space optimization focus",
      "Delhi: Weather-resistant materials needed"
    ],
    tips: [
      "Material costs vary by 15-30% across cities",
      "Local availability affects pricing",
      "Consider transportation costs"
    ]
  },

  buildingType: {
    title: "Building Type",
    description: "Type of construction based on intended use and design requirements.",
    examples: [
      "Residential: Family homes, apartments",
      "Commercial: Offices, shops, restaurants",
      "Mixed-use: Residential + commercial"
    ],
    tips: [
      "Affects structural requirements",
      "Different approval processes",
      "Varies in cost per sq ft"
    ]
  },

  bedrooms: {
    title: "Number of Bedrooms",
    description: "Total bedrooms including master bedroom. This affects electrical, plumbing, and finishing costs.",
    examples: [
      "1 BHK: 1 bedroom",
      "2 BHK: 2 bedrooms",
      "3 BHK: 3 bedrooms"
    ],
    tips: [
      "Consider future family needs",
      "Each bedroom needs electrical points",
      "Affects ventilation requirements"
    ]
  },

  bathrooms: {
    title: "Number of Bathrooms",
    description: "Total bathrooms including attached and common bathrooms. Major cost factor for plumbing and fixtures.",
    examples: [
      "2 BHK: Usually 2 bathrooms",
      "3 BHK: Usually 2-3 bathrooms",
      "4 BHK: Usually 3-4 bathrooms"
    ],
    tips: [
      "Attached bathrooms cost more",
      "Consider powder room for guests",
      "Waterproofing is crucial"
    ],
    warnings: [
      "Poor plumbing can cause major issues",
      "Ensure proper ventilation"
    ]
  },

  qualityTier: {
    title: "Quality Tier",
    description: "Overall quality level affecting materials, finishes, and fixtures throughout the project.",
    examples: [
      "Smart: Good quality, cost-effective",
      "Premium: Branded materials, better finishes",
      "Luxury: High-end materials, premium finishes"
    ],
    tips: [
      "Affects resale value significantly",
      "Consider long-term maintenance",
      "Balance cost with quality needs"
    ]
  },

  flooringType: {
    title: "Flooring Type",
    description: "Primary flooring material for living areas. Different areas may have different flooring.",
    examples: [
      "Vitrified tiles: Durable, easy maintenance",
      "Marble: Premium look, higher cost",
      "Wooden: Warm feel, requires maintenance"
    ],
    tips: [
      "Consider climate and usage",
      "Factor in maintenance costs",
      "Different rooms may need different flooring"
    ]
  },

  kitchenType: {
    title: "Kitchen Type",
    description: "Kitchen design and fitting level. Major cost component affecting overall project budget.",
    examples: [
      "Basic: Simple design, standard fittings",
      "Modular: Custom cabinets, modern appliances",
      "Premium: High-end materials, luxury appliances"
    ],
    tips: [
      "Consider cooking habits",
      "Storage needs vary by family size",
      "Good ventilation is essential"
    ]
  },

  parkingSpaces: {
    title: "Parking Spaces",
    description: "Number of vehicle parking spaces. Can be open or covered, affects site planning.",
    examples: [
      "1 car: 9x18 feet minimum",
      "2 cars: 18x18 feet or 9x36 feet",
      "With turning space: Add 3-4 feet"
    ],
    tips: [
      "Consider future vehicle needs",
      "Covered parking costs more",
      "Check local parking requirements"
    ]
  }
};

// Help trigger component
export interface HelpTriggerProps {
  helpId: string;
  content?: HelpContent;
  trigger?: 'click' | 'hover';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

export function HelpTrigger({ 
  helpId, 
  content, 
  trigger = 'hover', 
  size = 'md',
  className = '',
  children 
}: HelpTriggerProps) {
  const helpData = content || helpContent[helpId];
  
  if (!helpData) {
    return children || null;
  }

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <div className={`inline-flex items-center ${className}`}>
      {children}
      <button
        type="button"
        className={`ml-1 text-gray-400 hover:text-gray-600 transition-colors ${sizeClasses[size]}`}
        title={helpData.title}
        aria-label={`Help for ${helpData.title}`}
      >
        <svg
          className={sizeClasses[size]}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </button>
    </div>
  );
}

export default helpContent;
