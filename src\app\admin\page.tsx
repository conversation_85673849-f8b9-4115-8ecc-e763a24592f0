/**
 * Admin Dashboard Page
 * Day 5 Task 1: Admin Panel Foundation
 * Main dashboard with statistics and overview
 */

'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Calculator,
  FileText,
  TrendingUp,
  Package,
  DollarSign,
  Activity,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Download,
  RefreshCw,
} from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { cn } from '@/lib/utils';

// Mock data
const mockStats = {
  totalUsers: 2847,
  totalProjects: 1293,
  totalRevenue: 45600000, // 45.6L
  totalMaterials: 156,
  userGrowth: 12.5,
  projectGrowth: 23.1,
  revenueGrowth: 18.2,
  materialGrowth: 8.3,
};

const mockRecentUsers = [
  { id: '1', name: '<PERSON><PERSON>', email: 'r<PERSON><PERSON>@example.com', joinedAt: '2024-01-22', projects: 3 },
  { id: '2', name: '<PERSON><PERSON>', email: '<EMAIL>', joinedAt: '2024-01-22', projects: 1 },
  { id: '3', name: 'Amit <PERSON>', email: '<EMAIL>', joinedAt: '2024-01-21', projects: 2 },
  { id: '4', name: 'Sneha Reddy', email: '<EMAIL>', joinedAt: '2024-01-21', projects: 0 },
  { id: '5', name: 'Vikram Singh', email: '<EMAIL>', joinedAt: '2024-01-20', projects: 4 },
];

const mockRecentActivity = [
  { id: '1', action: 'User Registration', user: 'Rajesh Kumar', time: '2 minutes ago', type: 'user' },
  { id: '2', action: 'Project Created', user: 'Priya Sharma', time: '15 minutes ago', type: 'project' },
  { id: '3', action: 'Material Updated', user: 'Admin', time: '1 hour ago', type: 'material' },
  { id: '4', action: 'Payment Received', user: 'Amit Patel', time: '2 hours ago', type: 'payment' },
  { id: '5', action: 'Support Ticket', user: 'Sneha Reddy', time: '3 hours ago', type: 'support' },
];

export default function AdminDashboardPage() {
  const [refreshing, setRefreshing] = useState(false);

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) return `₹${(amount / 10000000).toFixed(1)}Cr`;
    if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString('en-IN');
  };

  const stats = [
    {
      title: "Total Users",
      value: formatNumber(mockStats.totalUsers),
      icon: Users,
      trend: `+${mockStats.userGrowth}%`,
      trendUp: true,
      color: "bg-blue-500",
    },
    {
      title: "Total Projects",
      value: formatNumber(mockStats.totalProjects),
      icon: Calculator,
      trend: `+${mockStats.projectGrowth}%`,
      trendUp: true,
      color: "bg-green-500",
    },
    {
      title: "Revenue",
      value: formatCurrency(mockStats.totalRevenue),
      icon: DollarSign,
      trend: `+${mockStats.revenueGrowth}%`,
      trendUp: true,
      color: "bg-purple-500",
    },
    {
      title: "Materials",
      value: formatNumber(mockStats.totalMaterials),
      icon: Package,
      trend: `+${mockStats.materialGrowth}%`,
      trendUp: true,
      color: "bg-yellow-500",
    },
  ];

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user': return Users;
      case 'project': return Calculator;
      case 'material': return Package;
      case 'payment': return DollarSign;
      case 'support': return FileText;
      default: return Activity;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'user': return 'text-blue-600 bg-blue-100';
      case 'project': return 'text-green-600 bg-green-100';
      case 'material': return 'text-yellow-600 bg-yellow-100';
      case 'payment': return 'text-purple-600 bg-purple-100';
      case 'support': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Manage your platform and monitor performance
          </p>
        </div>
        <div className="flex items-center gap-3">
          <EnhancedButton
            variant="outline"
            leftIcon={<RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />}
            onClick={handleRefresh}
            disabled={refreshing}
          >
            Refresh
          </EnhancedButton>
          <EnhancedButton
            variant="outline"
            leftIcon={<Download className="h-4 w-4" />}
          >
            Export Report
          </EnhancedButton>
          <EnhancedButton
            variant="default"
            leftIcon={<Plus className="h-4 w-4" />}
          >
            Quick Action
          </EnhancedButton>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <EnhancedCard variant="default" size="md" className="hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <div className="flex items-center mt-2">
                      {stat.trendUp ? (
                        <ArrowUpRight className="h-4 w-4 text-green-600" />
                      ) : (
                        <ArrowDownRight className="h-4 w-4 text-red-600" />
                      )}
                      <span className={cn(
                        "text-sm font-medium ml-1",
                        stat.trendUp ? "text-green-600" : "text-red-600"
                      )}>
                        {stat.trend}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">from last month</span>
                    </div>
                  </div>
                  <div className={cn("p-3 rounded-lg", stat.color)}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>
            </EnhancedCard>
          </motion.div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <EnhancedCard variant="default" size="lg">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
                <EnhancedButton variant="ghost" size="sm">
                  View All
                </EnhancedButton>
              </div>
              <div className="space-y-4">
                {mockRecentActivity.map((activity) => {
                  const IconComponent = getActivityIcon(activity.type);
                  return (
                    <div key={activity.id} className="flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                      <div className={cn("p-2 rounded-lg", getActivityColor(activity.type))}>
                        <IconComponent className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{activity.action}</p>
                        <p className="text-sm text-gray-600">by {activity.user}</p>
                      </div>
                      <div className="text-sm text-gray-500">
                        {activity.time}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Recent Users */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <EnhancedCard variant="default" size="lg">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Recent Users</h3>
                <EnhancedButton variant="ghost" size="sm">
                  View All
                </EnhancedButton>
              </div>
              <div className="space-y-4">
                {mockRecentUsers.map((user) => (
                  <div key={user.id} className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-medium text-sm">
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{user.name}</p>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <p className="text-xs text-gray-500">
                        {user.projects} projects • Joined {user.joinedAt}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <EnhancedCard variant="default" size="lg">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <EnhancedButton
                variant="outline"
                className="h-20 flex-col gap-2"
                leftIcon={<Package className="h-6 w-6" />}
              >
                <span>Add Material</span>
                <span className="text-xs text-gray-500">Manage inventory</span>
              </EnhancedButton>
              <EnhancedButton
                variant="outline"
                className="h-20 flex-col gap-2"
                leftIcon={<Users className="h-6 w-6" />}
              >
                <span>Manage Users</span>
                <span className="text-xs text-gray-500">User administration</span>
              </EnhancedButton>
              <EnhancedButton
                variant="outline"
                className="h-20 flex-col gap-2"
                leftIcon={<DollarSign className="h-6 w-6" />}
              >
                <span>Update Pricing</span>
                <span className="text-xs text-gray-500">Price management</span>
              </EnhancedButton>
              <EnhancedButton
                variant="outline"
                className="h-20 flex-col gap-2"
                leftIcon={<FileText className="h-6 w-6" />}
              >
                <span>View Reports</span>
                <span className="text-xs text-gray-500">Analytics & insights</span>
              </EnhancedButton>
            </div>
          </div>
        </EnhancedCard>
      </motion.div>
    </div>
  );
}
