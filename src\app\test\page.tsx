/**
 * Simple Test Page
 * Minimal page to test if Next.js can start without memory issues
 */

export default function TestPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Test Page
        </h1>
        <p className="text-gray-600 mb-8">
          If you can see this, Next.js is working!
        </p>
        <div className="space-y-2">
          <p className="text-sm text-gray-500">Environment: {process.env.NODE_ENV}</p>
          <p className="text-sm text-gray-500">Timestamp: {new Date().toISOString()}</p>
        </div>
      </div>
    </div>
  );
}
