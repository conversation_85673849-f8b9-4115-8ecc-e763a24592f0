/**
 * Simple Test Page
 * Minimal page to test if Next.js can start without memory issues
 */

export default function TestPage() {
  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50'>
      <div className='text-center p-8'>
        <h1 className='text-4xl font-bold text-gray-900 mb-4'>
          🎉 Next.js is Working!
        </h1>
        <p className='text-gray-600 mb-8'>
          Foundation repair successful - development server is running
        </p>
        <div className='space-y-2 bg-white p-4 rounded-lg shadow'>
          <p className='text-sm text-gray-500'>
            Environment: {process.env.NODE_ENV}
          </p>
          <p className='text-sm text-gray-500'>
            Timestamp: {new Date().toISOString()}
          </p>
          <p className='text-sm text-green-600 font-semibold'>
            ✅ Phase 1: Foundation Repair - COMPLETE
          </p>
        </div>
        <div className='mt-8 space-x-4'>
          <a
            href='/'
            className='inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700'
          >
            Go to Homepage
          </a>
          <a
            href='/calculator'
            className='inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'
          >
            Go to Calculator
          </a>
        </div>
      </div>
    </div>
  );
}
