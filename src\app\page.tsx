/**
 * Homepage Component
 * Professional landing page for Nirmaan AI Construction Calculator
 */

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Calculator, Home as HomeIcon, Users, BarChart3 } from 'lucide-react';

export default function Home() {
  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100'>
      {/* Header */}
      <header className='bg-white shadow-sm'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-6'>
            <div className='flex items-center'>
              <Calculator className='h-8 w-8 text-blue-600' />
              <span className='ml-2 text-xl font-bold text-gray-900'>
                Nirmaan AI
              </span>
            </div>
            <nav className='hidden md:flex space-x-8'>
              <Link
                href='/calculator'
                className='text-gray-500 hover:text-gray-900'
              >
                Calculator
              </Link>
              <Link
                href='/auth/login'
                className='text-gray-500 hover:text-gray-900'
              >
                Login
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
        <div className='text-center'>
          <h1 className='text-4xl md:text-6xl font-bold text-gray-900 mb-6'>
            Smart Construction
            <span className='text-blue-600'> Cost Calculator</span>
          </h1>
          <p className='text-xl text-gray-600 mb-8 max-w-3xl mx-auto'>
            Get accurate construction cost estimates with our AI-powered
            calculator. Professional-grade calculations with 95%+ accuracy for
            your dream home.
          </p>

          <div className='flex flex-col sm:flex-row gap-4 justify-center'>
            <Link href='/calculator'>
              <Button size='lg' className='w-full sm:w-auto'>
                <Calculator className='mr-2 h-5 w-5' />
                Start Calculating
              </Button>
            </Link>
            <Link href='/auth/signup'>
              <Button variant='outline' size='lg' className='w-full sm:w-auto'>
                Create Account
              </Button>
            </Link>
          </div>
        </div>

        {/* Features */}
        <div className='mt-20 grid grid-cols-1 md:grid-cols-3 gap-8'>
          <div className='text-center p-6 bg-white rounded-lg shadow-sm'>
            <HomeIcon className='h-12 w-12 text-blue-600 mx-auto mb-4' />
            <h3 className='text-lg font-semibold mb-2'>Accurate Estimates</h3>
            <p className='text-gray-600'>
              95%+ accuracy with IS code compliance and regional pricing
            </p>
          </div>

          <div className='text-center p-6 bg-white rounded-lg shadow-sm'>
            <Users className='h-12 w-12 text-blue-600 mx-auto mb-4' />
            <h3 className='text-lg font-semibold mb-2'>Expert Guidance</h3>
            <p className='text-gray-600'>
              Smart recommendations and professional insights
            </p>
          </div>

          <div className='text-center p-6 bg-white rounded-lg shadow-sm'>
            <BarChart3 className='h-12 w-12 text-blue-600 mx-auto mb-4' />
            <h3 className='text-lg font-semibold mb-2'>Detailed Reports</h3>
            <p className='text-gray-600'>
              Professional PDF reports with charts and breakdowns
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
