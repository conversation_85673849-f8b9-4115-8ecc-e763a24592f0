<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nirmaan AI Calculator - UI Showcase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for demonstration */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .button-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        .form-input {
            transition: all 0.3s ease;
            border: 2px solid #e2e8f0;
        }
        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .step-indicator {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .step-indicator.inactive {
            background: #e2e8f0;
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-slide-in {
            animation: slideIn 0.6s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold text-center">Nirmaan AI Construction Calculator</h1>
            <p class="text-center mt-2 text-blue-100">Professional Construction Cost Estimation for India</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Progress Indicator -->
        <div class="mb-8">
            <div class="flex justify-center items-center space-x-4">
                <div class="flex items-center">
                    <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                    <span class="ml-2 text-sm font-medium text-gray-700">Basic Info</span>
                </div>
                <div class="w-12 h-1 bg-purple-500"></div>
                <div class="flex items-center">
                    <div class="step-indicator inactive w-8 h-8 rounded-full flex items-center justify-center text-gray-500 text-sm font-bold">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Room Config</span>
                </div>
                <div class="w-12 h-1 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="step-indicator inactive w-8 h-8 rounded-full flex items-center justify-center text-gray-500 text-sm font-bold">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Quality</span>
                </div>
                <div class="w-12 h-1 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="step-indicator inactive w-8 h-8 rounded-full flex items-center justify-center text-gray-500 text-sm font-bold">4</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Features</span>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-xl card-shadow p-8 animate-fade-in">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Step 1: Project Basics</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Plot Size -->
                    <div class="animate-slide-in">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Plot Size (sq ft)
                            <span class="text-blue-500 ml-1 cursor-help" title="Total area of your land plot">ℹ️</span>
                        </label>
                        <input type="number" 
                               class="form-input w-full px-4 py-3 rounded-lg outline-none" 
                               placeholder="e.g., 1200"
                               value="1200">
                        <p class="text-xs text-gray-500 mt-1">💡 Recommended: 60-70% for built-up area</p>
                    </div>

                    <!-- Built-up Area -->
                    <div class="animate-slide-in" style="animation-delay: 0.1s">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Built-up Area (sq ft)
                            <span class="text-blue-500 ml-1 cursor-help" title="Covered area including walls">ℹ️</span>
                        </label>
                        <input type="number" 
                               class="form-input w-full px-4 py-3 rounded-lg outline-none" 
                               placeholder="e.g., 800"
                               value="800">
                        <p class="text-xs text-green-600 mt-1">✨ Smart suggestion: 800 sq ft (optimal for your plot)</p>
                    </div>

                    <!-- Floors -->
                    <div class="animate-slide-in" style="animation-delay: 0.2s">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Number of Floors</label>
                        <select class="form-input w-full px-4 py-3 rounded-lg outline-none">
                            <option value="">Select floors</option>
                            <option value="0">Ground only (G)</option>
                            <option value="1" selected>Ground + 1st (G+1)</option>
                            <option value="2">Ground + 2 floors (G+2)</option>
                            <option value="3">Ground + 3 floors (G+3)</option>
                        </select>
                    </div>

                    <!-- Location -->
                    <div class="animate-slide-in" style="animation-delay: 0.3s">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Construction Location</label>
                        <select class="form-input w-full px-4 py-3 rounded-lg outline-none">
                            <option value="">Select city</option>
                            <option value="bangalore" selected>Bangalore</option>
                            <option value="mumbai">Mumbai</option>
                            <option value="delhi">Delhi</option>
                            <option value="chennai">Chennai</option>
                            <option value="hyderabad">Hyderabad</option>
                            <option value="pune">Pune</option>
                        </select>
                        <p class="text-xs text-blue-600 mt-1">📍 Premium quality popular in Bangalore</p>
                    </div>
                </div>

                <!-- Live Cost Preview -->
                <div class="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">💰 Live Cost Preview</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">₹18,00,000</div>
                            <div class="text-sm text-gray-600">Smart Choice</div>
                            <div class="text-xs text-gray-500">₹2,250/sq ft</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">₹24,00,000</div>
                            <div class="text-sm text-gray-600">Premium Selection</div>
                            <div class="text-xs text-gray-500">₹3,000/sq ft</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">₹32,00,000</div>
                            <div class="text-sm text-gray-600">Luxury Collection</div>
                            <div class="text-xs text-gray-500">₹4,000/sq ft</div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between mt-8">
                    <button class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                        ← Previous
                    </button>
                    <button class="button-primary px-8 py-3 text-white rounded-lg font-medium">
                        Next: Room Configuration →
                    </button>
                </div>
            </div>
        </div>

        <!-- Feature Highlights -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white p-6 rounded-lg card-shadow text-center animate-fade-in">
                <div class="text-3xl mb-3">🎯</div>
                <h3 class="font-semibold text-gray-900 mb-2">Smart Defaults</h3>
                <p class="text-sm text-gray-600">Intelligent suggestions based on location and area</p>
            </div>
            <div class="bg-white p-6 rounded-lg card-shadow text-center animate-fade-in" style="animation-delay: 0.2s">
                <div class="text-3xl mb-3">📱</div>
                <h3 class="font-semibold text-gray-900 mb-2">Mobile Optimized</h3>
                <p class="text-sm text-gray-600">Touch-friendly interface with haptic feedback</p>
            </div>
            <div class="bg-white p-6 rounded-lg card-shadow text-center animate-fade-in" style="animation-delay: 0.4s">
                <div class="text-3xl mb-3">⚡</div>
                <h3 class="font-semibold text-gray-900 mb-2">Real-time Updates</h3>
                <p class="text-sm text-gray-600">Live cost calculations as you make selections</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 Nirmaan AI. Professional Construction Cost Estimation.</p>
            <p class="text-sm text-gray-400 mt-2">Enhanced MVP - Day 1 & Day 2 Complete</p>
        </div>
    </footer>

    <script>
        // Simple interactivity demonstration
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate form interactions
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                });
                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Simulate cost updates
            const plotSizeInput = document.querySelector('input[placeholder="e.g., 1200"]');
            if (plotSizeInput) {
                plotSizeInput.addEventListener('input', function() {
                    // Simulate real-time cost calculation
                    console.log('Cost calculation triggered for plot size:', this.value);
                });
            }
        });
    </script>
</body>
</html>
