/**
 * Smart Defaults System
 * Provides intelligent suggestions and auto-completion for form fields
 */

import { useState, useEffect, useCallback } from 'react';

export interface SmartSuggestion {
  field: string;
  value: string | number | boolean;
  reason: string;
  confidence: number;
  canApply: boolean;
  source: 'location' | 'area' | 'market' | 'user_pattern' | 'best_practice';
}

export interface SmartDefaultsOptions {
  context?: Record<string, any>;
  enabled?: boolean;
  autoApply?: boolean;
  confidenceThreshold?: number;
}

export interface SmartDefaultsHook {
  suggestions: SmartSuggestion[];
  applySuggestion: (field: string, value: any) => void;
  dismissSuggestion: (field: string) => void;
}

// Smart defaults rules based on Indian construction patterns
const SMART_RULES = {
  // Plot size based suggestions
  builtUpArea: (plotSize: number) => {
    if (plotSize <= 1200) return plotSize * 0.6; // 60% for small plots
    if (plotSize <= 2400) return plotSize * 0.65; // 65% for medium plots
    return plotSize * 0.7; // 70% for large plots
  },

  // Location based suggestions
  locationDefaults: {
    bangalore: {
      qualityTier: 'premium',
      parkingSpaces: '2',
      reason: 'Premium quality and covered parking are popular in Bangalore'
    },
    mumbai: {
      qualityTier: 'luxury',
      parkingSpaces: '1',
      reason: 'Space constraints in Mumbai favor compact designs'
    },
    delhi: {
      qualityTier: 'premium',
      parkingSpaces: '2',
      reason: 'Premium finishes and parking are standard in Delhi NCR'
    },
    chennai: {
      qualityTier: 'smart',
      parkingSpaces: '2',
      reason: 'Smart choices with good parking are preferred in Chennai'
    },
    hyderabad: {
      qualityTier: 'premium',
      parkingSpaces: '2',
      reason: 'Premium quality with parking is common in Hyderabad'
    },
    pune: {
      qualityTier: 'premium',
      parkingSpaces: '2',
      reason: 'Premium finishes are popular in Pune'
    }
  },

  // Area based room suggestions
  roomSuggestions: (builtUpArea: number) => {
    if (builtUpArea <= 800) {
      return { bedrooms: '2', bathrooms: '2', reason: 'Optimal for compact homes' };
    }
    if (builtUpArea <= 1200) {
      return { bedrooms: '3', bathrooms: '2', reason: 'Standard for medium homes' };
    }
    if (builtUpArea <= 1800) {
      return { bedrooms: '3', bathrooms: '3', reason: 'Comfortable for larger homes' };
    }
    return { bedrooms: '4', bathrooms: '3', reason: 'Spacious layout for large homes' };
  },

  // Quality tier suggestions based on area and location
  qualityTierSuggestion: (builtUpArea: number, location: string) => {
    const locationDefaults = SMART_RULES.locationDefaults[location.toLowerCase() as keyof typeof SMART_RULES.locationDefaults];
    if (locationDefaults) {
      return locationDefaults.qualityTier;
    }
    
    if (builtUpArea >= 2000) return 'luxury';
    if (builtUpArea >= 1200) return 'premium';
    return 'smart';
  }
};

export function useSmartDefaults(
  data: Record<string, any>,
  updateData: (updates: Record<string, any>) => void,
  options: SmartDefaultsOptions = {}
): SmartDefaultsHook {
  const {
    context = {},
    enabled = true,
    autoApply = false,
    confidenceThreshold = 0.7
  } = options;

  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]);
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set());

  const generateSuggestions = useCallback(() => {
    if (!enabled) return [];

    const newSuggestions: SmartSuggestion[] = [];
    const plotSize = parseFloat(data.plotSize || '0');
    const builtUpArea = parseFloat(data.builtUpArea || '0');
    const location = data.location || '';

    // Built-up area suggestion based on plot size
    if (plotSize > 0 && !data.builtUpArea && !dismissedSuggestions.has('builtUpArea')) {
      const suggestedArea = SMART_RULES.builtUpArea(plotSize);
      newSuggestions.push({
        field: 'builtUpArea',
        value: Math.round(suggestedArea),
        reason: `Optimal built-up area for ${plotSize} sq ft plot`,
        confidence: 0.8,
        canApply: true,
        source: 'area'
      });
    }

    // Room configuration suggestions
    if (builtUpArea > 0 && !data.bedrooms && !dismissedSuggestions.has('bedrooms')) {
      const roomSuggestion = SMART_RULES.roomSuggestions(builtUpArea);
      newSuggestions.push({
        field: 'bedrooms',
        value: roomSuggestion.bedrooms,
        reason: roomSuggestion.reason,
        confidence: 0.75,
        canApply: true,
        source: 'area'
      });
    }

    if (builtUpArea > 0 && !data.bathrooms && !dismissedSuggestions.has('bathrooms')) {
      const roomSuggestion = SMART_RULES.roomSuggestions(builtUpArea);
      newSuggestions.push({
        field: 'bathrooms',
        value: roomSuggestion.bathrooms,
        reason: roomSuggestion.reason,
        confidence: 0.75,
        canApply: true,
        source: 'area'
      });
    }

    // Location-based suggestions
    if (location && !data.qualityTier && !dismissedSuggestions.has('qualityTier')) {
      const qualityTier = SMART_RULES.qualityTierSuggestion(builtUpArea, location);
      const locationDefault = SMART_RULES.locationDefaults[location.toLowerCase() as keyof typeof SMART_RULES.locationDefaults];
      
      newSuggestions.push({
        field: 'qualityTier',
        value: qualityTier,
        reason: locationDefault?.reason || `${qualityTier} quality is popular in ${location}`,
        confidence: 0.7,
        canApply: true,
        source: 'location'
      });
    }

    // Parking suggestions
    if (location && !data.parkingSpaces && !dismissedSuggestions.has('parkingSpaces')) {
      const locationDefault = SMART_RULES.locationDefaults[location.toLowerCase() as keyof typeof SMART_RULES.locationDefaults];
      if (locationDefault) {
        newSuggestions.push({
          field: 'parkingSpaces',
          value: locationDefault.parkingSpaces,
          reason: locationDefault.reason,
          confidence: 0.65,
          canApply: true,
          source: 'location'
        });
      }
    }

    return newSuggestions.filter(s => s.confidence >= confidenceThreshold);
  }, [data, enabled, confidenceThreshold, dismissedSuggestions]);

  const applySuggestion = useCallback((field: string, value: any) => {
    updateData({ [field]: value });
    setSuggestions(prev => prev.filter(s => s.field !== field));
  }, [updateData]);

  const dismissSuggestion = useCallback((field: string) => {
    setDismissedSuggestions(prev => new Set([...prev, field]));
    setSuggestions(prev => prev.filter(s => s.field !== field));
  }, []);

  useEffect(() => {
    const newSuggestions = generateSuggestions();
    setSuggestions(newSuggestions);

    // Auto-apply high-confidence suggestions if enabled
    if (autoApply) {
      newSuggestions
        .filter(s => s.confidence >= 0.9)
        .forEach(s => applySuggestion(s.field, s.value));
    }
  }, [generateSuggestions, autoApply, applySuggestion]);

  return {
    suggestions,
    applySuggestion,
    dismissSuggestion
  };
}

// Smart badge component for displaying suggestion confidence
export interface SmartBadgeProps {
  type: 'recommended' | 'suggested' | 'optional';
  confidence: number;
  className?: string;
}

export function SmartBadge({ type, confidence, className }: SmartBadgeProps) {
  const getVariant = () => {
    if (confidence >= 0.8) return 'recommended';
    if (confidence >= 0.6) return 'suggested';
    return 'optional';
  };

  const variant = getVariant();
  const colors = {
    recommended: 'bg-green-100 text-green-800 border-green-200',
    suggested: 'bg-blue-100 text-blue-800 border-blue-200',
    optional: 'bg-gray-100 text-gray-800 border-gray-200'
  };

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colors[variant]} ${className}`}>
      {variant === 'recommended' && '⭐ '}
      {variant === 'suggested' && '💡 '}
      {variant === 'optional' && '💭 '}
      {type}
    </span>
  );
}

export default useSmartDefaults;
